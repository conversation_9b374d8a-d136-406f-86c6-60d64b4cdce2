using Unity.Entities;
using Unity.Mathematics;

namespace GPUAnimationCrowds
{
    // Component to track the character's movement state
    public struct CharacterMovementState : IComponentData
    {
        // Current velocity magnitude
        public float Speed;
        
        // Whether the character is currently moving
        public bool IsMoving;
        
        // Whether the character is currently attacking
        public bool IsAttacking;
        
        // Current movement direction
        public float3 Direction;
        
        // Current animation ID
        public int CurrentAnimationID;
        public float NormalizedTime;
    }
}
