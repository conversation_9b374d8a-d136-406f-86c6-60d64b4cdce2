﻿#region copyright
// -------------------------------------------------------
// Copyright (C) <PERSON><PERSON><PERSON><PERSON> [https://codestage.net]
// -------------------------------------------------------
#endregion

namespace CodeStage.Maintainer.Issues.Detectors
{
	using Core;
	using Core.Scan;

	internal interface ISettingsAssetBeginIssueDetector : IAssetBeginScanListener<DetectorResults>
	{
		AssetSettingsKind SettingsKind { get; }
	}
}