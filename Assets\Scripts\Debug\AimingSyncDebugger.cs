using UnityEngine;
using Module;
using Module.Mono.Animancer.RealsticFemale;
using Sirenix.OdinInspector;

namespace DebugAiming
{
    /// <summary>
    /// Debug component to monitor and test the aiming sync system
    /// </summary>
    public class AimingSyncDebugger : MonoBehaviour
    {
        [Header("Components")]
        [SerializeField] private MovementModule movementModule;
        [SerializeField] private RootMotion.Demos.RagdollAimingController ragdollAiming;
        
        [Header("Debug Info")]
        [ReadOnly] [SerializeField] private bool isAiming;
        [ReadOnly] [SerializeField] private bool hasAimTarget;
        [ReadOnly] [SerializeField] private Vector3 aimTargetPosition;
        [ReadOnly] [SerializeField] private float playerRotationY;
        [ReadOnly] [SerializeField] private float aimAngle;
        [ReadOnly] [SerializeField] private float angleDifference;
        [ReadOnly] [SerializeField] private bool shouldSync;
        
        [Head<PERSON>("Settings")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private bool showDebugGizmos = true;
        
        private void Start()
        {
            if (movementModule == null)
                movementModule = GetComponent<MovementModule>();
                
            if (ragdollAiming == null)
                ragdollAiming = GetComponentInChildren<RootMotion.Demos.RagdollAimingController>();
        }
        
        private void Update()
        {
            UpdateDebugInfo();
        }
        
        private void UpdateDebugInfo()
        {
            if (movementModule?.characterParameters == null) return;
            
            isAiming = movementModule.characterParameters.IsAiming;
            playerRotationY = transform.eulerAngles.y;
            
            if (ragdollAiming != null)
            {
                Transform aimTarget = ragdollAiming.GetPointer();
                hasAimTarget = aimTarget != null;
                
                if (hasAimTarget)
                {
                    aimTargetPosition = aimTarget.position;
                    
                    // Calculate aim angle
                    Vector3 aimDirection = (aimTargetPosition - transform.position);
                    aimDirection.y = 0;
                    
                    if (aimDirection.magnitude > 0.1f)
                    {
                        aimAngle = Mathf.Atan2(aimDirection.x, aimDirection.z) * Mathf.Rad2Deg;
                        angleDifference = Mathf.DeltaAngle(playerRotationY, aimAngle);
                        shouldSync = Mathf.Abs(angleDifference) > 5f;
                    }
                }
            }
            
            if (enableDebugLogging && isAiming && hasAimTarget)
            {
                UnityEngine.Debug.Log($"[AimingSyncDebugger] Aiming: {isAiming}, Target: {hasAimTarget}, " +
                                    $"Player: {playerRotationY:F1}°, Aim: {aimAngle:F1}°, " +
                                    $"Diff: {angleDifference:F1}°, Should Sync: {shouldSync}");
            }
        }
        
        [Button("Force Enable Aiming")]
        public void ForceEnableAiming()
        {
            if (movementModule?.characterParameters != null)
            {
                movementModule.characterParameters.IsAiming = true;
                UnityEngine.Debug.Log("[AimingSyncDebugger] Aiming force enabled");
            }
        }
        
        [Button("Force Disable Aiming")]
        public void ForceDisableAiming()
        {
            if (movementModule?.characterParameters != null)
            {
                movementModule.characterParameters.IsAiming = false;
                UnityEngine.Debug.Log("[AimingSyncDebugger] Aiming force disabled");
            }
        }
        
        [Button("Create Test Target")]
        public void CreateTestTarget()
        {
            if (ragdollAiming != null)
            {
                GameObject testTarget = new GameObject("TestAimTarget");
                testTarget.transform.position = transform.position + transform.forward * 5f + Vector3.right * 3f;
                
                // Set as ragdoll target
                ragdollAiming.target = testTarget.transform;
                
                UnityEngine.Debug.Log($"[AimingSyncDebugger] Test target created at {testTarget.transform.position}");
            }
        }
        
        private void OnDrawGizmos()
        {
            if (!showDebugGizmos) return;
            
            // Draw player forward direction
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(transform.position, transform.position + transform.forward * 2f);
            
            // Draw aim target and direction
            if (hasAimTarget)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(aimTargetPosition, 0.3f);
                
                Vector3 aimDirection = (aimTargetPosition - transform.position);
                aimDirection.y = 0;
                aimDirection.Normalize();
                
                Gizmos.color = Color.yellow;
                Gizmos.DrawLine(transform.position, transform.position + aimDirection * 2f);
                
                // Draw angle difference arc
                if (shouldSync)
                {
                    Gizmos.color = Color.green;
                    Vector3 center = transform.position + Vector3.up * 0.5f;
                    
                    // Simple arc representation
                    for (int i = 0; i < 10; i++)
                    {
                        float t = i / 9f;
                        float angle = Mathf.Lerp(playerRotationY, aimAngle, t) * Mathf.Deg2Rad;
                        Vector3 point = center + new Vector3(Mathf.Sin(angle), 0, Mathf.Cos(angle)) * 1.5f;
                        
                        if (i > 0)
                        {
                            float prevAngle = Mathf.Lerp(playerRotationY, aimAngle, (i-1) / 9f) * Mathf.Deg2Rad;
                            Vector3 prevPoint = center + new Vector3(Mathf.Sin(prevAngle), 0, Mathf.Cos(prevAngle)) * 1.5f;
                            Gizmos.DrawLine(prevPoint, point);
                        }
                    }
                }
            }
            
            // Draw status indicators
            Vector3 statusPos = transform.position + Vector3.up * 3f;
            
            if (isAiming)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(statusPos, Vector3.one * 0.3f);
            }
            
            if (shouldSync)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawWireCube(statusPos + Vector3.up * 0.5f, Vector3.one * 0.2f);
            }
        }
    }
}
