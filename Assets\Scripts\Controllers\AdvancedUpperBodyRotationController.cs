using UnityEngine;
using Sirenix.OdinInspector;
using Unity.Mathematics;
using Events;
using RootMotion.FinalIK;

namespace Controllers
{
    /// <summary>
    /// Advanced upper body rotation controller that provides dynamic rotation limits,
    /// spine chain distribution, and movement-aware blending
    /// </summary>
    public class AdvancedUpperBodyRotationController : MonoBehaviour
    {
        [Header("Rotation Limit Components")]
        [SerializeField] private RotationLimitAngle baseRotationLimit;
        [SerializeField] private Transform spineRoot;
        [SerializeField] private Transform[] spineChain;
        [SerializeField] private Transform headTransform;

        [Header("Dynamic Rotation Limits")]
        [SerializeField] private float standingMaxRotation = 60f; // Degrees
        [SerializeField] private float movingMaxRotation = 45f; // Degrees
        [SerializeField] private float aimingMaxRotation = 75f; // Degrees
        [SerializeField] private float combatMaxRotation = 90f; // Degrees

        [Header("Spine Chain Distribution")]
        [SerializeField] private bool enableSpineChainDistribution = true;
        [SerializeField] private AnimationCurve spineDistributionCurve = AnimationCurve.EaseInOut(0, 0.2f, 1, 1.0f);
        [SerializeField] private float spineFlexibility = 0.8f; // How much spine can contribute vs head
        [SerializeField] private float headContribution = 0.6f; // How much head contributes to total rotation

        [Header("Comfort Zone System")]
        [SerializeField] private bool enableComfortZone = true;
        [SerializeField] private float comfortZoneAngle = 30f; // Degrees - no resistance
        [SerializeField] private float maxComfortAngle = 50f; // Degrees - start resistance
        [SerializeField] private AnimationCurve comfortResistanceCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float maxResistanceForce = 0.8f; // Maximum resistance multiplier

        [Header("Movement-Aware Blending")]
        [SerializeField] private bool enableMovementAwareBlending = true;
        [SerializeField] private float movementBlendTime = 0.3f;
        [SerializeField] private AnimationCurve movementBlendCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private float movementSpeedThreshold = 0.1f;

        [Header("Target-Based Rotation")]
        [SerializeField] private bool enableTargetBasedRotation = true;
        [SerializeField] private bool rotateEntirePlayer = true; // NEW: Rotate entire player GameObject
        [SerializeField] private float targetRotationSpeed = 3f;
        [SerializeField] private float targetRotationSmoothTime = 0.2f;
        [SerializeField] private float maxTargetRotationAngle = 120f; // Degrees
        [SerializeField] private float playerRotationThreshold = 30f; // Angle threshold for player rotation
        [SerializeField] private float playerRotationSpeed = 2f; // Speed for player GameObject rotation

        [Header("Stability Settings")]
        [SerializeField] private bool preventRapidTargetSwitching = true;
        [SerializeField] private float targetSwitchCooldown = 0.3f; // Minimum time between target switches
        [SerializeField] private float maxRotationSpeed = 180f; // Max degrees per second
        [SerializeField] private bool disableWhenRagdollActive = true; // Disable when RagdollAimingController is active

        [Header("Performance Settings")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool enablePerformanceOptimization = true;
        [SerializeField] private float updateInterval = 0.016f; // 60 FPS

        // State tracking
        private bool isAiming;
        private bool isMoving;
        private bool isCombat;
        private float currentMovementSpeed;
        private float3 currentTarget;
        private Vector3 playerForward;

        // Rotation state
        private float currentRotationAngle;
        private float targetRotationAngle;
        private float rotationVelocity;
        private float currentMaxRotation;
        private float targetMaxRotation;
        private float maxRotationVelocity;

        // Player rotation state
        private float currentPlayerRotation;
        private float targetPlayerRotation;
        private float playerRotationVelocity;
        private bool shouldRotatePlayer;

        // Stability tracking
        private float lastTargetSwitchTime;
        private float3 lastStableTarget;
        private float lastRotationUpdateTime;
        private bool isRagdollAimingActive;

        // Spine chain state
        private Quaternion[] originalSpineRotations;
        private Quaternion[] targetSpineRotations;
        private float[] spineRotationWeights;

        // Comfort zone state
        private float comfortZoneResistance;
        private float lastComfortZoneUpdate;

        // Performance optimization
        private float lastUpdateTime;
        private int frameCount;
        private const int OPTIMIZATION_FRAME_INTERVAL = 2; // Update every 2 frames when optimizing

        #region Unity Lifecycle

        private void Awake()
        {
            InitializeRotationController();
        }

        private void Start()
        {
            SubscribeToEvents();
            CacheOriginalRotations();
        }

        private void Update()
        {
            if (ShouldUpdateThisFrame())
            {
                UpdateRotationSystem();
            }
        }

        private void LateUpdate()
        {
            if (enableSpineChainDistribution)
            {
                ApplySpineChainRotation();
            }
        }

        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the rotation controller
        /// </summary>
        private void InitializeRotationController()
        {
            // Initialize spine chain if not set
            if (spineChain == null || spineChain.Length == 0)
            {
                AutoDetectSpineChain();
            }

            // Initialize arrays
            if (spineChain != null)
            {
                originalSpineRotations = new Quaternion[spineChain.Length];
                targetSpineRotations = new Quaternion[spineChain.Length];
                spineRotationWeights = new float[spineChain.Length];

                CalculateSpineWeights();
            }

            currentMaxRotation = standingMaxRotation;
            targetMaxRotation = standingMaxRotation;

            LogDebug("AdvancedUpperBodyRotationController initialized");
        }

        /// <summary>
        /// Auto-detect spine chain from hierarchy
        /// </summary>
        private void AutoDetectSpineChain()
        {
            if (spineRoot == null)
            {
                LogDebug("Spine root not set, attempting auto-detection");
                return;
            }

            // Simple spine detection - find transforms with "spine" in name
            var spineTransforms = new System.Collections.Generic.List<Transform>();
            FindSpineTransforms(spineRoot, spineTransforms);

            if (spineTransforms.Count > 0)
            {
                spineChain = spineTransforms.ToArray();
                LogDebug($"Auto-detected {spineChain.Length} spine transforms");
            }
        }

        /// <summary>
        /// Recursively find spine transforms
        /// </summary>
        private void FindSpineTransforms(Transform parent, System.Collections.Generic.List<Transform> spineList)
        {
            if (parent.name.ToLower().Contains("spine") || parent.name.ToLower().Contains("chest"))
            {
                spineList.Add(parent);
            }

            for (int i = 0; i < parent.childCount; i++)
            {
                FindSpineTransforms(parent.GetChild(i), spineList);
            }
        }

        /// <summary>
        /// Calculate weights for spine chain distribution
        /// </summary>
        private void CalculateSpineWeights()
        {
            if (spineChain == null) return;

            for (int i = 0; i < spineChain.Length; i++)
            {
                float normalizedIndex = (float)i / (spineChain.Length - 1);
                spineRotationWeights[i] = spineDistributionCurve.Evaluate(normalizedIndex) * spineFlexibility;
            }
        }

        /// <summary>
        /// Cache original spine rotations
        /// </summary>
        private void CacheOriginalRotations()
        {
            if (spineChain == null) return;

            for (int i = 0; i < spineChain.Length; i++)
            {
                originalSpineRotations[i] = spineChain[i].localRotation;
                targetSpineRotations[i] = spineChain[i].localRotation;
            }
        }

        #endregion

        #region Event Handling

        /// <summary>
        /// Subscribe to relevant events
        /// </summary>
        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);

            LogDebug("Subscribed to rotation-related events");
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);

            LogDebug("Unsubscribed from rotation-related events");
        }

        /// <summary>
        /// Handle aiming started event
        /// </summary>
        private void OnAimingStarted(OnAimingOnTargetEvent eventData)
        {
            isAiming = true;
            currentTarget = eventData.TargetPosition;
            UpdateRotationLimits();

            LogDebug($"Aiming started at target: {currentTarget}");
        }

        /// <summary>
        /// Handle aiming stopped event
        /// </summary>
        private void OnAimingStopped(OnUnAimingTargetEvent eventData)
        {
            isAiming = false;
            currentTarget = float3.zero;
            UpdateRotationLimits();

            LogDebug("Aiming stopped");
        }

        /// <summary>
        /// Handle target detection event
        /// </summary>
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            currentTarget = eventData.TargetPosition;
            isCombat = true;
            UpdateRotationLimits();

            LogDebug($"Target detected: {currentTarget}");
        }

        /// <summary>
        /// Handle target lost event
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            currentTarget = float3.zero;
            isCombat = false;
            UpdateRotationLimits();

            LogDebug("Target lost");
        }

        #endregion

        #region Rotation System Update

        /// <summary>
        /// Check if we should update rotation system this frame
        /// </summary>
        private bool ShouldUpdateThisFrame()
        {
            frameCount++;

            if (enablePerformanceOptimization)
            {
                // Update every nth frame when optimizing
                if (frameCount % OPTIMIZATION_FRAME_INTERVAL != 0)
                    return false;
            }

            // Always update if enough time has passed
            return Time.time - lastUpdateTime >= updateInterval;
        }

        /// <summary>
        /// Update the rotation system
        /// </summary>
        private void UpdateRotationSystem()
        {
            lastUpdateTime = Time.time;

            // Update movement state
            UpdateMovementState();

            // Update rotation limits based on current context
            UpdateRotationLimits();

            // Calculate target rotation angle
            CalculateTargetRotation();

            // Apply comfort zone resistance
            ApplyComfortZoneResistance();

            // Update current rotation with smoothing
            UpdateCurrentRotation();

            // Update player rotation if enabled
            UpdatePlayerRotation();

            // Apply rotation limits
            ApplyRotationLimits();

            LogDebug($"Rotation updated: Current={currentRotationAngle:F1}°, Target={targetRotationAngle:F1}°, Max={currentMaxRotation:F1}°");
        }

        /// <summary>
        /// Update movement state tracking
        /// </summary>
        private void UpdateMovementState()
        {
            // This would typically come from a movement component
            // For now, we'll estimate based on transform changes
            Vector3 currentPosition = transform.position;
            Vector3 currentForward = transform.forward;

            // Update movement speed (simplified)
            currentMovementSpeed = 0f; // This should be set from movement system
            isMoving = currentMovementSpeed > movementSpeedThreshold;

            playerForward = currentForward;
        }

        /// <summary>
        /// Update rotation limits based on current context
        /// </summary>
        private void UpdateRotationLimits()
        {
            // Determine target max rotation based on context
            if (isCombat)
                targetMaxRotation = combatMaxRotation;
            else if (isAiming)
                targetMaxRotation = aimingMaxRotation;
            else if (isMoving)
                targetMaxRotation = movingMaxRotation;
            else
                targetMaxRotation = standingMaxRotation;

            // Smooth transition between limits
            if (enableMovementAwareBlending)
            {
                currentMaxRotation = Mathf.SmoothDamp(currentMaxRotation, targetMaxRotation,
                    ref maxRotationVelocity, movementBlendTime);
            }
            else
            {
                currentMaxRotation = targetMaxRotation;
            }
        }

        /// <summary>
        /// Calculate target rotation angle based on target position
        /// Handles both upper body rotation and full player rotation with stability checks
        /// </summary>
        private void CalculateTargetRotation()
        {
            if (!enableTargetBasedRotation || currentTarget.Equals(float3.zero))
            {
                targetRotationAngle = 0f;
                targetPlayerRotation = 0f;
                shouldRotatePlayer = false;
                return;
            }

            // Check if RagdollAimingController is active and should take priority
            if (disableWhenRagdollActive && IsRagdollAimingActive())
            {
                targetRotationAngle = 0f;
                targetPlayerRotation = 0f;
                shouldRotatePlayer = false;
                LogDebug("RagdollAimingController is active, disabling upper body rotation");
                return;
            }

            // Calculate direction to target
            Vector3 directionToTarget = (Vector3)currentTarget - transform.position;
            directionToTarget.y = 0; // Keep horizontal

            if (directionToTarget.magnitude < 0.1f)
            {
                targetRotationAngle = 0f;
                targetPlayerRotation = 0f;
                shouldRotatePlayer = false;
                return;
            }

            // Calculate angle between forward and target direction
            float angleToTarget = Vector3.SignedAngle(playerForward, directionToTarget, Vector3.up);

            // Apply rotation speed limiting to prevent visual bugs
            float maxAngleChange = maxRotationSpeed * Time.deltaTime;
            float angleDifference = Mathf.DeltaAngle(targetRotationAngle, angleToTarget);

            if (Mathf.Abs(angleDifference) > maxAngleChange)
            {
                // Limit the angle change to prevent rapid spinning
                angleToTarget = targetRotationAngle + Mathf.Sign(angleDifference) * maxAngleChange;
                LogDebug($"Limiting rotation speed: {angleDifference:F1}° -> {maxAngleChange:F1}°");
            }

            // Determine if we should rotate the entire player or just upper body
            if (rotateEntirePlayer && Mathf.Abs(angleToTarget) > playerRotationThreshold)
            {
                // Rotate entire player for large angles
                shouldRotatePlayer = true;
                targetPlayerRotation = angleToTarget;

                // Reduce upper body rotation since player is rotating
                float remainingAngle = angleToTarget - (angleToTarget * 0.7f); // Player handles 70% of rotation
                targetRotationAngle = Mathf.Clamp(remainingAngle, -currentMaxRotation, currentMaxRotation);

                LogDebug($"Player rotation: {targetPlayerRotation:F1}°, Upper body: {targetRotationAngle:F1}°");
            }
            else
            {
                // Only upper body rotation for smaller angles
                shouldRotatePlayer = false;
                targetPlayerRotation = 0f;
                targetRotationAngle = Mathf.Clamp(angleToTarget, -maxTargetRotationAngle, maxTargetRotationAngle);

                LogDebug($"Upper body only rotation: {targetRotationAngle:F1}°");
            }
        }

        /// <summary>
        /// Apply comfort zone resistance
        /// </summary>
        private void ApplyComfortZoneResistance()
        {
            if (!enableComfortZone) return;

            float absAngle = Mathf.Abs(targetRotationAngle);

            if (absAngle <= comfortZoneAngle)
            {
                comfortZoneResistance = 0f;
            }
            else if (absAngle <= maxComfortAngle)
            {
                float resistanceProgress = (absAngle - comfortZoneAngle) / (maxComfortAngle - comfortZoneAngle);
                comfortZoneResistance = comfortResistanceCurve.Evaluate(resistanceProgress) * maxResistanceForce;
            }
            else
            {
                comfortZoneResistance = maxResistanceForce;
            }

            // Apply resistance to target angle
            targetRotationAngle *= (1f - comfortZoneResistance);
        }

        /// <summary>
        /// Update current rotation with smoothing
        /// </summary>
        private void UpdateCurrentRotation()
        {
            currentRotationAngle = Mathf.SmoothDampAngle(currentRotationAngle, targetRotationAngle,
                ref rotationVelocity, targetRotationSmoothTime);
        }

        /// <summary>
        /// Update player GameObject rotation when enabled
        /// </summary>
        private void UpdatePlayerRotation()
        {
            if (!rotateEntirePlayer || !shouldRotatePlayer)
            {
                // Reset player rotation smoothly when not needed
                if (Mathf.Abs(currentPlayerRotation) > 0.1f)
                {
                    currentPlayerRotation = Mathf.SmoothDampAngle(currentPlayerRotation, 0f,
                        ref playerRotationVelocity, targetRotationSmoothTime * 1.5f);
                    ApplyPlayerRotation();
                }
                return;
            }

            // Update player rotation with smoothing
            currentPlayerRotation = Mathf.SmoothDampAngle(currentPlayerRotation, targetPlayerRotation,
                ref playerRotationVelocity, targetRotationSmoothTime / playerRotationSpeed);

            // Apply the rotation to the player GameObject
            ApplyPlayerRotation();

            LogDebug($"Player rotation updated: Current={currentPlayerRotation:F1}°, Target={targetPlayerRotation:F1}°");
        }

        /// <summary>
        /// Apply rotation to the player GameObject
        /// </summary>
        private void ApplyPlayerRotation()
        {
            if (transform != null)
            {
                // Get current rotation and apply the calculated rotation
                Vector3 currentEuler = transform.eulerAngles;
                float newYRotation = currentEuler.y + currentPlayerRotation;

                // Apply the rotation
                transform.rotation = Quaternion.Euler(currentEuler.x, newYRotation, currentEuler.z);

                // Update player forward direction
                playerForward = transform.forward;
            }
        }

        /// <summary>
        /// Apply rotation limits
        /// </summary>
        private void ApplyRotationLimits()
        {
            currentRotationAngle = Mathf.Clamp(currentRotationAngle, -currentMaxRotation, currentMaxRotation);

            // Update base rotation limit component if available
            if (baseRotationLimit != null)
            {
                baseRotationLimit.limit = currentMaxRotation;
            }
        }

        #endregion

        #region Spine Chain Rotation

        /// <summary>
        /// Apply distributed rotation across spine chain
        /// </summary>
        private void ApplySpineChainRotation()
        {
            if (spineChain == null || spineChain.Length == 0) return;

            // Calculate total rotation to distribute
            float totalRotation = currentRotationAngle;

            // Distribute rotation across spine chain
            for (int i = 0; i < spineChain.Length; i++)
            {
                float spineRotation = totalRotation * spineRotationWeights[i];

                // Apply rotation around Y-axis (horizontal rotation)
                Quaternion rotationDelta = Quaternion.AngleAxis(spineRotation, Vector3.up);
                targetSpineRotations[i] = originalSpineRotations[i] * rotationDelta;

                // Smooth transition to target rotation
                spineChain[i].localRotation = Quaternion.Slerp(
                    spineChain[i].localRotation,
                    targetSpineRotations[i],
                    Time.deltaTime * targetRotationSpeed);
            }

            // Apply head rotation if available
            if (headTransform != null)
            {
                float headRotation = totalRotation * headContribution;
                Quaternion headRotationDelta = Quaternion.AngleAxis(headRotation, Vector3.up);
                Quaternion targetHeadRotation = headTransform.localRotation * headRotationDelta;

                headTransform.localRotation = Quaternion.Slerp(
                    headTransform.localRotation,
                    targetHeadRotation,
                    Time.deltaTime * targetRotationSpeed);
            }
        }

        #endregion

        #region Public API

        /// <summary>
        /// Get current rotation angle
        /// </summary>
        public float GetCurrentRotationAngle()
        {
            return currentRotationAngle;
        }

        /// <summary>
        /// Get target rotation angle
        /// </summary>
        public float GetTargetRotationAngle()
        {
            return targetRotationAngle;
        }

        /// <summary>
        /// Get current maximum rotation limit
        /// </summary>
        public float GetCurrentMaxRotation()
        {
            return currentMaxRotation;
        }

        /// <summary>
        /// Get comfort zone resistance value
        /// </summary>
        public float GetComfortZoneResistance()
        {
            return comfortZoneResistance;
        }

        /// <summary>
        /// Set movement speed for rotation calculations
        /// </summary>
        public void SetMovementSpeed(float speed)
        {
            currentMovementSpeed = speed;
            isMoving = speed > movementSpeedThreshold;
        }

        /// <summary>
        /// Set target position for rotation calculations with stability checks
        /// </summary>
        public void SetTargetPosition(Vector3 targetPosition)
        {
            // Check if RagdollAimingController is active and should take priority
            if (disableWhenRagdollActive && IsRagdollAimingActive())
            {
                LogDebug("RagdollAimingController is active, skipping upper body rotation");
                return;
            }

            // Prevent rapid target switching
            if (preventRapidTargetSwitching && Time.time - lastTargetSwitchTime < targetSwitchCooldown)
            {
                LogDebug($"Target switch too rapid, ignoring. Cooldown: {targetSwitchCooldown - (Time.time - lastTargetSwitchTime):F2}s remaining");
                return;
            }

            // Check if target has changed significantly
            if (!currentTarget.Equals(float3.zero) && math.distance(currentTarget, targetPosition) < 0.5f)
            {
                // Target hasn't moved much, just update without resetting cooldown
                currentTarget = targetPosition;
                return;
            }

            // Significant target change
            currentTarget = targetPosition;
            lastStableTarget = targetPosition;
            lastTargetSwitchTime = Time.time;

            LogDebug($"Target position set to: {targetPosition}");
        }

        /// <summary>
        /// Override rotation limits temporarily
        /// </summary>
        public void OverrideRotationLimits(float newLimit, float duration = 0f)
        {
            currentMaxRotation = newLimit;

            if (duration > 0f)
            {
                StartCoroutine(ResetRotationLimitsAfterDelay(duration));
            }
        }

        /// <summary>
        /// Reset rotation to neutral position
        /// </summary>
        public void ResetToNeutral(float transitionTime = 0.5f)
        {
            targetRotationAngle = 0f;
            targetRotationSmoothTime = transitionTime;

            LogDebug("Resetting rotation to neutral position");
        }

        /// <summary>
        /// Get rotation status for debugging
        /// </summary>
        public RotationStatusDebug GetRotationStatus()
        {
            return new RotationStatusDebug
            {
                CurrentAngle = currentRotationAngle,
                TargetAngle = targetRotationAngle,
                MaxRotation = currentMaxRotation,
                ComfortResistance = comfortZoneResistance,
                IsAiming = isAiming,
                IsMoving = isMoving,
                IsCombat = isCombat,
                MovementSpeed = currentMovementSpeed,
                PlayerRotationAngle = currentPlayerRotation,
                TargetPlayerRotation = targetPlayerRotation,
                ShouldRotatePlayer = shouldRotatePlayer,
                RotateEntirePlayerEnabled = rotateEntirePlayer,
                IsRagdollAimingActive = isRagdollAimingActive,
                LastTargetSwitchTime = lastTargetSwitchTime,
                PreventRapidSwitching = preventRapidTargetSwitching
            };
        }

        /// <summary>
        /// Get current player rotation angle
        /// </summary>
        public float GetCurrentPlayerRotation()
        {
            return currentPlayerRotation;
        }

        /// <summary>
        /// Get target player rotation angle
        /// </summary>
        public float GetTargetPlayerRotation()
        {
            return targetPlayerRotation;
        }

        /// <summary>
        /// Check if player should be rotated
        /// </summary>
        public bool ShouldRotatePlayer()
        {
            return shouldRotatePlayer && rotateEntirePlayer;
        }

        /// <summary>
        /// Enable or disable entire player rotation
        /// </summary>
        public void SetRotateEntirePlayer(bool enable)
        {
            rotateEntirePlayer = enable;
            LogDebug($"Rotate entire player set to: {enable}");
        }

        /// <summary>
        /// Set player rotation threshold
        /// </summary>
        public void SetPlayerRotationThreshold(float threshold)
        {
            playerRotationThreshold = Mathf.Clamp(threshold, 0f, 180f);
            LogDebug($"Player rotation threshold set to: {playerRotationThreshold}°");
        }

        /// <summary>
        /// Check if RagdollAimingController is active and should take priority
        /// </summary>
        private bool IsRagdollAimingActive()
        {
            // Try to find RagdollAimingController on this GameObject or its children
            var ragdollAiming = GetComponentInChildren<RootMotion.Demos.RagdollAimingController>();

            if (ragdollAiming != null)
            {
                // Check if it's enabled and has an active target
                isRagdollAimingActive = ragdollAiming.enabled && ragdollAiming.target != null && ragdollAiming.weight > 0.1f;
                return isRagdollAimingActive;
            }

            isRagdollAimingActive = false;
            return false;
        }

        /// <summary>
        /// Get current ragdoll aiming status
        /// </summary>
        public bool GetRagdollAimingStatus()
        {
            return isRagdollAimingActive;
        }

        /// <summary>
        /// Force rotation system update
        /// </summary>
        public void ForceUpdate()
        {
            UpdateRotationSystem();
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Reset rotation limits after delay
        /// </summary>
        private System.Collections.IEnumerator ResetRotationLimitsAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            UpdateRotationLimits();
            LogDebug("Rotation limits reset after delay");
        }

        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[AdvancedUpperBodyRotationController] {message}");
            }
        }

        /// <summary>
        /// Validate configuration values
        /// </summary>
        [Button("Validate Configuration")]
        private void ValidateConfiguration()
        {
            if (standingMaxRotation <= 0 || standingMaxRotation > 180)
            {
                Debug.LogWarning("[AdvancedUpperBodyRotationController] Standing max rotation should be between 0 and 180 degrees");
                standingMaxRotation = Mathf.Clamp(standingMaxRotation, 1f, 180f);
            }

            if (comfortZoneAngle >= maxComfortAngle)
            {
                Debug.LogWarning("[AdvancedUpperBodyRotationController] Comfort zone angle must be less than max comfort angle");
                maxComfortAngle = comfortZoneAngle + 10f;
            }

            if (spineFlexibility < 0 || spineFlexibility > 1)
            {
                Debug.LogWarning("[AdvancedUpperBodyRotationController] Spine flexibility must be between 0 and 1");
                spineFlexibility = Mathf.Clamp01(spineFlexibility);
            }

            LogDebug("Configuration validated");
        }

        #endregion

        #region Debug and Visualization

        private void OnDrawGizmosSelected()
        {
            if (!Application.isPlaying) return;

            Vector3 center = transform.position + Vector3.up * 1.5f;

            // Draw current upper body rotation angle
            Gizmos.color = Color.green;
            Vector3 currentDirection = Quaternion.AngleAxis(currentRotationAngle, Vector3.up) * transform.forward;
            Gizmos.DrawLine(center, center + currentDirection * 2f);

            // Draw target upper body rotation angle
            Gizmos.color = Color.blue;
            Vector3 targetDirection = Quaternion.AngleAxis(targetRotationAngle, Vector3.up) * transform.forward;
            Gizmos.DrawLine(center, center + targetDirection * 1.5f);

            // Draw player rotation (if enabled)
            if (rotateEntirePlayer && shouldRotatePlayer)
            {
                Gizmos.color = Color.cyan;
                Vector3 playerRotationDirection = Quaternion.AngleAxis(currentPlayerRotation, Vector3.up) * transform.forward;
                Gizmos.DrawLine(center, center + playerRotationDirection * 2.5f);

                // Draw player rotation threshold
                Gizmos.color = Color.white;
                Vector3 thresholdLeft = Quaternion.AngleAxis(-playerRotationThreshold, Vector3.up) * transform.forward;
                Vector3 thresholdRight = Quaternion.AngleAxis(playerRotationThreshold, Vector3.up) * transform.forward;
                Gizmos.DrawLine(center, center + thresholdLeft * 1.2f);
                Gizmos.DrawLine(center, center + thresholdRight * 1.2f);
            }

            // Draw rotation limits
            Gizmos.color = Color.red;
            Vector3 maxLeftDirection = Quaternion.AngleAxis(-currentMaxRotation, Vector3.up) * transform.forward;
            Vector3 maxRightDirection = Quaternion.AngleAxis(currentMaxRotation, Vector3.up) * transform.forward;
            Gizmos.DrawLine(center, center + maxLeftDirection);
            Gizmos.DrawLine(center, center + maxRightDirection);

            // Draw comfort zone
            if (enableComfortZone)
            {
                Gizmos.color = Color.yellow;
                Vector3 comfortLeftDirection = Quaternion.AngleAxis(-comfortZoneAngle, Vector3.up) * transform.forward;
                Vector3 comfortRightDirection = Quaternion.AngleAxis(comfortZoneAngle, Vector3.up) * transform.forward;
                Gizmos.DrawLine(center, center + comfortLeftDirection * 0.8f);
                Gizmos.DrawLine(center, center + comfortRightDirection * 0.8f);
            }

            // Draw target position
            if (!currentTarget.Equals(float3.zero))
            {
                Gizmos.color = Color.magenta;
                Gizmos.DrawWireSphere(currentTarget, 0.3f);
                Gizmos.DrawLine(center, currentTarget);
            }

            // Draw legend
            Vector3 legendPos = center + Vector3.up * 2f;
            if (rotateEntirePlayer)
            {
                // Visual legend for different rotation types
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(legendPos, Vector3.one * 0.1f); // Upper body

                if (shouldRotatePlayer)
                {
                    Gizmos.color = Color.cyan;
                    Gizmos.DrawWireCube(legendPos + Vector3.right * 0.3f, Vector3.one * 0.1f); // Player rotation
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// Rotation status for debugging
    /// </summary>
    [System.Serializable]
    public struct RotationStatusDebug
    {
        public float CurrentAngle;
        public float TargetAngle;
        public float MaxRotation;
        public float ComfortResistance;
        public bool IsAiming;
        public bool IsMoving;
        public bool IsCombat;
        public float MovementSpeed;
        public float PlayerRotationAngle;
        public float TargetPlayerRotation;
        public bool ShouldRotatePlayer;
        public bool RotateEntirePlayerEnabled;
        public bool IsRagdollAimingActive;
        public float LastTargetSwitchTime;
        public bool PreventRapidSwitching;

        public override string ToString()
        {
            return $"Rotation Status: UpperBody={CurrentAngle:F1}°→{TargetAngle:F1}°, Player={PlayerRotationAngle:F1}°→{TargetPlayerRotation:F1}°, " +
                   $"Max={MaxRotation:F1}°, Resistance={ComfortResistance:F2}, ShouldRotatePlayer={ShouldRotatePlayer}, " +
                   $"RagdollActive={IsRagdollAimingActive}, RapidSwitchPrevention={PreventRapidSwitching}, " +
                   $"Aiming={IsAiming}, Moving={IsMoving}, Combat={IsCombat}";
        }
    }
}
