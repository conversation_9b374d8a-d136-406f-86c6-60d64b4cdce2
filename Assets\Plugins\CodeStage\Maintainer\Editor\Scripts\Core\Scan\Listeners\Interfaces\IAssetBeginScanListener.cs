﻿#region copyright
// -------------------------------------------------------
// Copyright (C) <PERSON><PERSON><PERSON><PERSON> [https://codestage.net]
// -------------------------------------------------------
#endregion

namespace CodeStage.Maintainer.Core.Scan
{
	internal interface IAssetBeginScanListener<T> where T : IScanListenerResults
	{
		void AssetBegin(T results, AssetLocation location);
	}
}