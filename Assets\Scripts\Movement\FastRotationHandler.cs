using System;
using Events;
using Module.Mono.Animancer.RealsticFemale;
using UnityEngine;

namespace Module.Movement
{
    public class FastRotationHandler : MonoBehaviour
    {
        [SerializeField] private Transform playerTransform;
        [SerializeField] private float rotationSpeed = 720f; // Degrees per second for fast rotation
        
        private void Start()
        {
            if (playerTransform == null)
            {
                playerTransform = transform;
            }
            
            // Subscribe to the fast rotation event
            EventManager.Subscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);
        }

        private void OnDestroy()
        {
            // Unsubscribe when destroyed
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);
        }

        private void OnRequireFastRotation(OnRequireFastRotationEvent evt)
        {
            // Get the target position
            Vector3 targetPosition = evt.TargetPosition;
            
            // Calculate direction to target (ignore Y axis for horizontal rotation)
            Vector3 directionToTarget = targetPosition - playerTransform.position;
            directionToTarget.y = 0; // Keep rotation on horizontal plane
            directionToTarget.Normalize();
            
            // Calculate target rotation
            Quaternion targetRotation = Quaternion.LookRotation(directionToTarget);
            
            // Apply immediate rotation - for fast response to behind threats
            playerTransform.rotation = targetRotation;
            
            // Log for debugging
            Debug.unityLogger.Log($"Fast rotation applied towards target at {targetPosition}");
        }
    }
}
