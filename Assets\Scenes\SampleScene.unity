%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 10304, guid: 0000000000000000f000000000000000, type: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 112000002, guid: 04971e4c341eae245867172be0b8c691,
    type: 2}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!114 &6799139 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0dcbffa32d899b4cac41b83a61cef7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &35558000
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 855751751}
    m_Modifications:
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4829969537808851870, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_Name
      value: UIManager
      objectReference: {fileID: 0}
    - target: {fileID: 6627936706183307427, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: m_touchFollowerJoystick
      value: 
      objectReference: {fileID: 1604868500}
    - target: {fileID: 6636633659850530640, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: configuration
      value: 
      objectReference: {fileID: 11400000, guid: 82160471aa3291c4494239765c64a3c4,
        type: 2}
    - target: {fileID: 6636633659850530640, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      propertyPath: worldSpaceCanvas
      value: 
      objectReference: {fileID: 614247163}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 4829969537808851870, guid: 0010ea5588d4a184383249fc0ff264d4,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1281752712}
  m_SourcePrefab: {fileID: 100100000, guid: 0010ea5588d4a184383249fc0ff264d4, type: 3}
--- !u!1 &49294664
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 49294665}
  - component: {fileID: 49294669}
  - component: {fileID: 49294668}
  - component: {fileID: 49294667}
  - component: {fileID: 49294666}
  m_Layer: 0
  m_Name: Joystick2Knob
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &49294665
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49294664}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 552138590}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &49294666
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49294664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f6a0577d46b80a4f929e57370c60650, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetCamera: {fileID: 0}
  HorizontalAxisEnabled: 1
  VerticalAxisEnabled: 1
  MaxRangeMode: 0
  MaxRange: 1.5
  MaxRangeTransform: {fileID: 0}
  JoystickValue:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: RightJoystickMovement
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  JoystickNormalizedValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickMagnitudeValue:
    m_PersistentCalls:
      m_Calls: []
  OnPointerDownEvent:
    m_PersistentCalls:
      m_Calls: []
  OnDragEvent:
    m_PersistentCalls:
      m_Calls: []
  OnPointerUpEvent:
    m_PersistentCalls:
      m_Calls: []
  RotatingIndicator: {fileID: 0}
  RotatingIndicatorThreshold: 0.1
  PressedOpacity: 0.5
  InterpolateOpacity: 1
  InterpolateOpacitySpeed: 1
  RawValue: {x: 0, y: 0}
  NormalizedValue: {x: 0, y: 0}
  Magnitude: 0
  DrawGizmos: 1
--- !u!114 &49294667
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49294664}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.73140293, g: 1, b: 0.5424528, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &49294668
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49294664}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &49294669
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 49294664}
  m_CullTransparentMesh: 0
--- !u!1 &81119051
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 81119052}
  - component: {fileID: 81119055}
  - component: {fileID: 81119054}
  - component: {fileID: 81119053}
  m_Layer: 0
  m_Name: Joystick1KnobOutline
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &81119052
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81119051}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 229625701}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &81119053
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81119051}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 4636595778793708312, guid: f3ee7d861c6ac7e4a975285391fe761c,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &81119054
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81119051}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &81119055
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 81119051}
  m_CullTransparentMesh: 0
--- !u!1 &102065685
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 102065686}
  - component: {fileID: 102065689}
  - component: {fileID: 102065688}
  - component: {fileID: 102065687}
  m_Layer: 0
  m_Name: JoystickRKnob
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &102065686
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 102065685}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 367723575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -25.38, y: 2.0003052}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &102065687
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 102065685}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.8936883, b: 0.504717, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &102065688
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 102065685}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &102065689
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 102065685}
  m_CullTransparentMesh: 0
--- !u!114 &172621471 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5862402671225516785, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!224 &198118836 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 6714727979177705959, guid: 2da7ec3bcc0f24a939908d096ac0c351,
    type: 3}
  m_PrefabInstance: {fileID: 1341390262}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &205932700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 205932704}
  m_Layer: 0
  m_Name: UICanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &205932704
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 205932700}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -52.7, y: 0, z: -100}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1693928276}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &229625700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 229625701}
  - component: {fileID: 229625703}
  - component: {fileID: 229625704}
  - component: {fileID: 229625702}
  m_Layer: 0
  m_Name: JoystickRKnob
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &229625701
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229625700}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 81119052}
  - {fileID: 623714275}
  m_Father: {fileID: 1604868501}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 64.10962, y: -416.32062}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &229625702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229625700}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.5019608}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 4520387000713905209, guid: f3ee7d861c6ac7e4a975285391fe761c,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &229625703
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229625700}
  m_CullTransparentMesh: 0
--- !u!225 &229625704
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229625700}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!1 &233670226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 233670228}
  - component: {fileID: 233670227}
  m_Layer: 0
  m_Name: ModuleManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &233670227
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233670226}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2b583de9d25d424dac4e98b0acb82165, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: _modules
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _playerController: {fileID: 0}
--- !u!4 &233670228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 233670226}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2124832282}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!224 &250731758 stripped
RectTransform:
  m_CorrespondingSourceObject: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
    type: 3}
  m_PrefabInstance: {fileID: 1846172000}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &300860918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 300860919}
  - component: {fileID: 300860922}
  - component: {fileID: 300860921}
  - component: {fileID: 300860920}
  m_Layer: 0
  m_Name: JoystickRBackground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &300860919
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300860918}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1136780205}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 189.6352, y: 367.0559}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &300860920
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300860918}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300010, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &300860921
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300860918}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &300860922
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 300860918}
  m_CullTransparentMesh: 0
--- !u!1 &314017320
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 314017321}
  - component: {fileID: 314017323}
  - component: {fileID: 314017322}
  m_Layer: 0
  m_Name: DashesDecoration
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &314017321
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 314017320}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1761537834}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &314017322
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 314017320}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: -272858757991599442, guid: f3ee7d861c6ac7e4a975285391fe761c,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &314017323
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 314017320}
  m_CullTransparentMesh: 0
--- !u!1 &335063805 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1708842091693502, guid: d4529f47989526241929319abfdfc29d,
    type: 3}
  m_PrefabInstance: {fileID: 1494654533}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &335063809
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 335063805}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: -1
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 0
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!1 &336932667
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 336932668}
  - component: {fileID: 336932671}
  - component: {fileID: 336932670}
  - component: {fileID: 336932669}
  m_Layer: 0
  m_Name: JoystickRKnob
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &336932668
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 336932667}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1136780205}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -322.3648, y: -144.94409}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &336932669
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 336932667}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0.8936883, b: 0.504717, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &336932670
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 336932667}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &336932671
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 336932667}
  m_CullTransparentMesh: 0
--- !u!1 &340151031
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 340151032}
  - component: {fileID: 340151036}
  - component: {fileID: 340151035}
  - component: {fileID: 340151034}
  - component: {fileID: 340151033}
  m_Layer: 5
  m_Name: Canvas Scale H
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &340151032
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340151031}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1957787061}
  - {fileID: 1789951446}
  - {fileID: 2112996225}
  - {fileID: 465214845}
  - {fileID: 1822868573}
  - {fileID: 681784571}
  - {fileID: 198118836}
  m_Father: {fileID: 1706917448}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &340151033
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340151031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 24243a89fc1989f409217cfa65b50351, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &340151034
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340151031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 1048575
--- !u!114 &340151035
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340151031}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 1
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &340151036
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 340151031}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &367723574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 367723575}
  - component: {fileID: 367723579}
  - component: {fileID: 367723578}
  - component: {fileID: 367723577}
  - component: {fileID: 367723576}
  m_Layer: 0
  m_Name: FollowerJoystick
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &367723575
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367723574}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1748571131}
  - {fileID: 102065686}
  - {fileID: 1832267740}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 1228.653}
  m_Pivot: {x: 0.5, y: 0}
--- !u!114 &367723576
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367723574}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 12f6f90faaf30834980e50fda0c74a9c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetCamera: {fileID: 1494654542}
  HorizontalAxisEnabled: 1
  VerticalAxisEnabled: 1
  MaxRangeMode: 0
  MaxRange: 1.5
  MaxRangeTransform: {fileID: 0}
  JoystickValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickNormalizedValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickMagnitudeValue:
    m_PersistentCalls:
      m_Calls: []
  OnPointerDownEvent:
    m_PersistentCalls:
      m_Calls: []
  OnDragEvent:
    m_PersistentCalls:
      m_Calls: []
  OnPointerUpEvent:
    m_PersistentCalls:
      m_Calls: []
  RotatingIndicator: {fileID: 0}
  RotatingIndicatorThreshold: 0.1
  PressedOpacity: 0.5
  InterpolateOpacity: 1
  InterpolateOpacitySpeed: 1
  RawValue: {x: 0, y: 0}
  NormalizedValue: {x: 0, y: 0}
  Magnitude: 0
  DrawGizmos: 1
  KnobCanvasGroup: {fileID: 102065688}
  BackgroundCanvasGroup: {fileID: 1748571133}
  ResetPositionToInitialOnRelease: 1
  InterpolateFollowMovement: 1
  InterpolateFollowMovementSpeed: 0.3
  SpringFollowInterpolation: 0
  SpringDamping: 0.6
  SpringFrequency: 4
  ShouldConstrainBackground: 1
  BackgroundConstraintRectTransform: {fileID: 1832267740}
  BackgroundConstraintPaddingLeft: 0
  BackgroundConstraintPaddingRight: 0
  BackgroundConstraintPaddingTop: 0
  BackgroundConstraintPaddingBottom: 0
--- !u!114 &367723577
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367723574}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.6792453, g: 0, b: 0, a: 0.21960784}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &367723578
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367723574}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &367723579
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 367723574}
  m_CullTransparentMesh: 0
--- !u!1 &380025920
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 380025921}
  - component: {fileID: 380025924}
  - component: {fileID: 380025923}
  - component: {fileID: 380025922}
  m_Layer: 5
  m_Name: Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &380025921
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380025920}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1731716460}
  m_Father: {fileID: 1693928276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 106, y: -322}
  m_SizeDelta: {x: 276.49, y: 80}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &380025922
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380025920}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 380025923}
  m_OnClick:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &380025923
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380025920}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &380025924
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 380025920}
  m_CullTransparentMesh: 1
--- !u!114 &406832672 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 969609528223833965, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ad99c1b3efc4acb90269d1b9eb71c1e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &410087039
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 410087041}
  - component: {fileID: 410087040}
  - component: {fileID: 410087042}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &410087040
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 2
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 5000
  m_UseColorTemperature: 1
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!4 &410087041
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &410087042
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 410087039}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 1
--- !u!1 &417140643
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 417140644}
  - component: {fileID: 417140648}
  - component: {fileID: 417140647}
  - component: {fileID: 417140646}
  - component: {fileID: 417140645}
  m_Layer: 0
  m_Name: ArrowRight
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &417140644
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 417140643}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071067}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1914733753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -38.5, y: 120.4}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &417140645
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 417140643}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc509a62d8e6cfc45a8431d8d586b9f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AxisPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: RightPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: RightReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: HorizontalAxisPressed
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 1
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  PressedOpacity: 0.5
  AxisValue: 1
  MouseMode: 0
--- !u!114 &417140646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 417140643}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300014, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &417140647
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 417140643}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &417140648
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 417140643}
  m_CullTransparentMesh: 0
--- !u!1 &461586644 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1219460452532175012, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &461586646
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 461586644}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738, w: 0.99217784}
  limit: 40
  twistLimit: 40
--- !u!1 &465214844
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465214845}
  - component: {fileID: 465214847}
  - component: {fileID: 465214846}
  m_Layer: 5
  m_Name: Panel
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &465214845
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465214844}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 705652772}
  - {fileID: 1507738687}
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 383.53027, y: -52.100098}
  m_SizeDelta: {x: 666.05, y: 90.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &465214846
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465214844}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.13333334, g: 0.16862746, b: 0.21568629, a: 0.5882353}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &465214847
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 465214844}
  m_CullTransparentMesh: 0
--- !u!1 &467649617
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 467649621}
  - component: {fileID: 467649620}
  - component: {fileID: 467649619}
  m_Layer: 18
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!23 &467649619
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 467649617}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: eab44ca55f607964eaf12ebbc15a2e43, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &467649620
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 467649617}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &467649621
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 467649617}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1958131080}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &484082367
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 484082368}
  - component: {fileID: 484082370}
  - component: {fileID: 484082369}
  m_Layer: 0
  m_Name: Aim Target (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &484082368
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484082367}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.07337323, y: 1.332, z: 5.62}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!23 &484082369
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484082367}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &484082370
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 484082367}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!1 &503211930
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 503211931}
  - component: {fileID: 503211935}
  - component: {fileID: 503211934}
  - component: {fileID: 503211933}
  - component: {fileID: 503211932}
  m_Layer: 0
  m_Name: Joystick1Knob
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &503211931
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503211930}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2139117039}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -0.00024414062, y: 0.00024414062}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &503211932
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503211930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8f6a0577d46b80a4f929e57370c60650, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetCamera: {fileID: 0}
  HorizontalAxisEnabled: 1
  VerticalAxisEnabled: 1
  MaxRangeMode: 0
  MaxRange: 2
  MaxRangeTransform: {fileID: 0}
  JoystickValue:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: LeftJoystickMovement
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  JoystickNormalizedValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickMagnitudeValue:
    m_PersistentCalls:
      m_Calls: []
  OnPointerDownEvent:
    m_PersistentCalls:
      m_Calls: []
  OnDragEvent:
    m_PersistentCalls:
      m_Calls: []
  OnPointerUpEvent:
    m_PersistentCalls:
      m_Calls: []
  RotatingIndicator: {fileID: 0}
  RotatingIndicatorThreshold: 0.1
  PressedOpacity: 0.5
  InterpolateOpacity: 1
  InterpolateOpacitySpeed: 1
  RawValue: {x: 0, y: 0}
  NormalizedValue: {x: 0, y: 0}
  Magnitude: 0
  DrawGizmos: 1
--- !u!114 &503211933
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503211930}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.5613208, g: 0.840603, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &503211934
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503211930}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &503211935
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 503211930}
  m_CullTransparentMesh: 0
--- !u!1 &552138589
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 552138590}
  - component: {fileID: 552138593}
  - component: {fileID: 552138592}
  - component: {fileID: 552138591}
  m_Layer: 0
  m_Name: Joystick2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &552138590
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 552138589}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 49294665}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 826, y: -1303}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &552138591
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 552138589}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300010, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &552138592
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 552138589}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &552138593
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 552138589}
  m_CullTransparentMesh: 0
--- !u!114 &603274978 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3042903506368811451, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9db18cd58f72348905fc8ae0a482d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &614247162
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 614247166}
  - component: {fileID: 614247163}
  - component: {fileID: 614247165}
  - component: {fileID: 614247164}
  m_Layer: 5
  m_Name: WorldSpaceCanvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!223 &614247163
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614247162}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 1494654542}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 688246547
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &614247164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614247162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 4194303
--- !u!114 &614247165
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614247162}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!224 &614247166
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 614247162}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.08, y: 0.08, z: 0.08}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 292.5, y: 448.5}
  m_SizeDelta: {x: 585, y: 897}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1 &621580494
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 621580495}
  - component: {fileID: 621580499}
  - component: {fileID: 621580498}
  - component: {fileID: 621580497}
  - component: {fileID: 621580496}
  m_Layer: 0
  m_Name: ArrowLeft
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &621580495
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 621580494}
  m_LocalRotation: {x: 0, y: 0, z: -0.7071068, w: 0.7071067}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.24999996, y: 0.24999996, z: 0.25}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1914733753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -178.5, y: 120.4}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &621580496
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 621580494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc509a62d8e6cfc45a8431d8d586b9f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AxisPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: LeftPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: LeftReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: HorizontalAxisPressed
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: -1
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  PressedOpacity: 0.5
  AxisValue: -1
  MouseMode: 0
--- !u!114 &621580497
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 621580494}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300014, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &621580498
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 621580494}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &621580499
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 621580494}
  m_CullTransparentMesh: 0
--- !u!1 &623714274
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 623714275}
  - component: {fileID: 623714278}
  - component: {fileID: 623714277}
  - component: {fileID: 623714276}
  m_Layer: 0
  m_Name: Joystick1KnobArrows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &623714275
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623714274}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 229625701}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 192, y: 192}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &623714276
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623714274}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 4372357654241830872, guid: f3ee7d861c6ac7e4a975285391fe761c,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &623714277
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623714274}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &623714278
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 623714274}
  m_CullTransparentMesh: 0
--- !u!114 &676399464 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5572294579708353132, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a0b21ec855a7db42ab30d4665de8769, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &681784570
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 681784571}
  - component: {fileID: 681784573}
  - component: {fileID: 681784572}
  m_Layer: 5
  m_Name: Description
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &681784571
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681784570}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 533.59, y: -115.33}
  m_SizeDelta: {x: 392.96155, y: 30}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &681784572
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681784570}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 18
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 40
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Example 1 : Various ways to handle MCS events.'
--- !u!222 &681784573
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 681784570}
  m_CullTransparentMesh: 0
--- !u!1 &705652771
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 705652772}
  - component: {fileID: 705652774}
  - component: {fileID: 705652773}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &705652772
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705652771}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 465214845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.22822967, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.3300476, y: 0.14754105}
  m_SizeDelta: {x: 0.5, y: 0.29494}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &705652773
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705652771}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 40
    m_FontStyle: 1
    m_BestFit: 0
    m_MinSize: 4
    m_MaxSize: 41
    m_Alignment: 3
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Mobile Controller System
--- !u!222 &705652774
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 705652771}
  m_CullTransparentMesh: 0
--- !u!1 &715755610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 715755611}
  - component: {fileID: 715755615}
  - component: {fileID: 715755614}
  - component: {fileID: 715755613}
  - component: {fileID: 715755612}
  m_Layer: 0
  m_Name: MMSwipeZone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &715755611
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 715755610}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1941190959}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 1510.9999, y: -1425}
  m_SizeDelta: {x: 800, y: 800}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &715755612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 715755610}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5a68e615c17d2bf49a1a082e34fd9b6e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  MinimalSwipeLength: 50
  MaximumPressLength: 10
  ZoneSwiped:
    m_PersistentCalls:
      m_Calls: []
  ZonePressed:
    m_PersistentCalls:
      m_Calls: []
  MouseMode: 1
--- !u!114 &715755613
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 715755610}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.15405147, g: 0, b: 0.16037738, a: 0.30588236}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &715755614
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 715755610}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &715755615
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 715755610}
  m_CullTransparentMesh: 0
--- !u!1 &725519866
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 725519867}
  - component: {fileID: 725519868}
  m_Layer: 0
  m_Name: WeaponUpgradeManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &725519867
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 725519866}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 855751751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &725519868
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 725519866}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f47da939ac00bfa4da7372d5dffc7b4f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  updateInterval: 0.5
  availableWeapons:
  - {fileID: 11400000, guid: bbbe95b3e177620419a92a69d58824dd, type: 2}
  availableBullets:
  - {fileID: 11400000, guid: 8eea38d560f3025429bd0a7e12d39e24, type: 2}
--- !u!1 &731133086
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 731133087}
  - component: {fileID: 731133088}
  m_Layer: 0
  m_Name: DebugManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &731133087
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 731133086}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1081750593}
  m_Father: {fileID: 855751751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &731133088
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 731133086}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1cbb37e6a1ce5e24f8a5180c05b1e779, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _settings: {fileID: 0}
--- !u!114 &731518315 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5562475231938505990, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c08dd31e7f694fdcaf3d250569c31167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1001 &832293010
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"<SubModules>k__BackingField.{0es}","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 676399464}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 9
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"_modules","length":8'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"_modules.[0]","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[2]'
      value: '"path":"_modules.[1]","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[3]'
      value: '"path":"_modules.[2]","value":$eref:2'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[4]'
      value: '"path":"_modules.[3]","value":$eref:3'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[5]'
      value: '"path":"_modules.[4]","value":$eref:4'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[6]'
      value: '"path":"_modules.[5]","value":$eref:5'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[7]'
      value: '"path":"_modules.[6]","value":$eref:6'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[8]'
      value: '"path":"_modules.[7]","value":$eref:7'
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 8
      objectReference: {fileID: 0}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 1712231943}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 1020340877}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[2]'
      value: 
      objectReference: {fileID: 731518315}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[3]'
      value: 
      objectReference: {fileID: 902399118}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[4]'
      value: 
      objectReference: {fileID: 902399117}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[5]'
      value: 
      objectReference: {fileID: 1015305832}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[6]'
      value: 
      objectReference: {fileID: 1015305831}
    - target: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[7]'
      value: 
      objectReference: {fileID: 6799139}
    - target: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: DynamicLayeredCharacterAnimations
      value: 
      objectReference: {fileID: 1454374016}
    - target: {fileID: 3042903506368811451, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: animator
      value: 
      objectReference: {fileID: 1277964586}
    - target: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4031297349607152135, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_Name
      value: Female1New
      objectReference: {fileID: 0}
    - target: {fileID: 5887728694554286191, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: _AnimationManager
      value: 
      objectReference: {fileID: 1454374011}
    - target: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 5954690637389537017, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 6591187809807469422, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: moveSpeed
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: rotationSpeed
      value: 60
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: movementConfig
      value: 
      objectReference: {fileID: 11400000, guid: ecac2dc6864880d4fb362f2dade9c893,
        type: 2}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: improvedMovement
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_maxRotationSpeed
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_minRotationAngle
      value: 45
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_rotationCooldown
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_rotationSmoothTime
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: aimingSpeedMultiplier
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_targetRotationSpeed
      value: 0.1
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_useWholeBodyRotation
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: strafingSpeedMultiplier
      value: 0.2
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: backpedalSpeedMultiplier
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: m_rotationStabilityThreshold
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: aimingConfig
      value: 
      objectReference: {fileID: 11400000, guid: accec5b7d548020408105a645a93c3d4,
        type: 2}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"<SubModules>k__BackingField.{0es}","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"<SubModules>k__BackingField.{1es}","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 406832672}
    - target: {fileID: 7029741597687228658, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 1513624735}
    - target: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7421254585528994050, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModifications.Array.size
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[0]'
      value: '"path":"_needInputComponents.[0]","value":$eref:0'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[1]'
      value: '"path":"_needInputComponents.[1]","value":$eref:1'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[2]'
      value: '"path":"_needInputComponents.[2]","value":$eref:2'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[3]'
      value: '"path":"_needInputComponents.[3]","value":$eref:3'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[4]'
      value: '"path":"_needInputComponents.[4]","value":$eref:4'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModifications.Array.data[5]'
      value: '"path":"_needInputComponents.[5]","value":$eref:5'
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.PrefabModificationsReferencedUnityObjects.Array.size
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[0]'
      value: 
      objectReference: {fileID: 1454374007}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[1]'
      value: 
      objectReference: {fileID: 731518315}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[2]'
      value: 
      objectReference: {fileID: 1020340877}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[3]'
      value: 
      objectReference: {fileID: 6799139}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[4]'
      value: 
      objectReference: {fileID: 902399118}
    - target: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: 'serializationData.PrefabModificationsReferencedUnityObjects.Array.data[5]'
      value: 
      objectReference: {fileID: 902399117}
    - target: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8830726950650311852, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 8950689789528778046, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    - target: {fileID: 9167802454622032298, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: _Animancer
      value: 
      objectReference: {fileID: 1277964587}
    - target: {fileID: 9167802454622032298, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: _ActionMask
      value: 
      objectReference: {fileID: 31900000, guid: 2ee86d3a5e2d3cf4fa965c906da1eb50,
        type: 2}
    - target: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      propertyPath: serializationData.Prefab
      value: 
      objectReference: {fileID: 9195335255939506981, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
    m_RemovedComponents:
    - {fileID: 4324291176780966862, guid: 73df3382d6e5d1a4a9e6a91c8157ab90, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1454374025}
    - targetCorrespondingSourceObject: {fileID: 5319643890459908578, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1215299991}
    - targetCorrespondingSourceObject: {fileID: 1219460452532175012, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 461586646}
    - targetCorrespondingSourceObject: {fileID: 6852714991633896669, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 2020776546}
    - targetCorrespondingSourceObject: {fileID: 7195047958775627785, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1857699947}
  m_SourcePrefab: {fileID: 100100000, guid: 73df3382d6e5d1a4a9e6a91c8157ab90, type: 3}
--- !u!4 &832293011 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 1768001276132255913, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &832293012 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 1553757846737663554, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370782250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a14bfc2338bf4611b10d398151e4dca8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &832575517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 832575519}
  - component: {fileID: 832575518}
  m_Layer: 0
  m_Name: Global Volume
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &832575518
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832575517}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 172515602e62fb746b5d573b38a5fe58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsGlobal: 1
  priority: 0
  blendDistance: 0
  weight: 1
  sharedProfile: {fileID: 11400000, guid: a6560a915ef98420e9faacc1c7438823, type: 2}
--- !u!4 &832575519
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 832575517}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &841690212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 841690213}
  - component: {fileID: 841690215}
  - component: {fileID: 841690214}
  m_Layer: 5
  m_Name: Buttons
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &841690213
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 841690212}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1361600901}
  - {fileID: 1584897383}
  - {fileID: 960681741}
  - {fileID: 987441011}
  - {fileID: 1277722380}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -672, y: 542.8801}
  m_SizeDelta: {x: 1309.5244, y: 1085.7623}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &841690214
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 841690212}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &841690215
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 841690212}
  m_CullTransparentMesh: 0
--- !u!1 &847582821
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 847582824}
  - component: {fileID: 847582823}
  - component: {fileID: 847582822}
  m_Layer: 5
  m_Name: Text (TMP) (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &847582822
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 847582821}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: New Text
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 72
  m_fontSizeBase: 50
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &847582823
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 847582821}
  m_CullTransparentMesh: 1
--- !u!224 &847582824
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 847582821}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1693928276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 50, y: -179}
  m_SizeDelta: {x: 540.7159, y: 100.964}
  m_Pivot: {x: 0, y: 1}
--- !u!1 &855751750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 855751751}
  - component: {fileID: 855751752}
  m_Layer: 0
  m_Name: Managers
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &855751751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 855751750}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2124832282}
  - {fileID: 855762710}
  - {fileID: 725519867}
  - {fileID: 731133087}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &855751752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 855751750}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2573637d14a0c34488e43cbfb7c74236, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  cullingCameras:
  - {fileID: 1494654542}
  shadowCastingLights: []
  addEditorSceneCamera: 1
  urpAsset: {fileID: 0}
  drawCullingVolumes: 0
  cullingVolumeColor: {r: 0, g: 0, b: 1, a: 1}
  drawSceneBoundingBoxes: 0
  visibleChunkColor: {r: 0, g: 1, b: 0, a: 1}
  invisibleChunkColor: {r: 1, g: 0, b: 0, a: 1}
  visibleRendererColor: {r: 1, g: 1, b: 1, a: 1}
  invisibleRendererColor: {r: 1, g: 0, b: 0, a: 1}
--- !u!4 &855762710 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 4436200574500276309, guid: 0010ea5588d4a184383249fc0ff264d4,
    type: 3}
  m_PrefabInstance: {fileID: 35558000}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &871102699
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 871102700}
  - component: {fileID: 871102704}
  - component: {fileID: 871102703}
  - component: {fileID: 871102702}
  - component: {fileID: 871102701}
  m_Layer: 0
  m_Name: ArrowDown
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &871102700
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871102699}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1914733753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -108.5, y: 50.4}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &871102701
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871102699}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc509a62d8e6cfc45a8431d8d586b9f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AxisPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: DownPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: DownReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: VerticalAxisPressed
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: -1
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  PressedOpacity: 0.5
  AxisValue: -1
  MouseMode: 0
--- !u!114 &871102702
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871102699}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300014, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &871102703
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871102699}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &871102704
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 871102699}
  m_CullTransparentMesh: 0
--- !u!114 &902399117 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 2742023919547625688, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8691dd0ae36145d88c17f855c2eb91ba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &902399118 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 792671241793180101, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 88226de0bc8347118b6651808977535d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &960681740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 960681741}
  - component: {fileID: 960681745}
  - component: {fileID: 960681744}
  - component: {fileID: 960681743}
  - component: {fileID: 960681742}
  m_Layer: 0
  m_Name: ButtonY
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &960681741
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 960681740}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 841690213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -620.0001, y: 769}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &960681742
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 960681740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Interactable: 1
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: YPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: YReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: YPressed
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 0.3
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 0
--- !u!114 &960681743
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 960681740}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300006, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &960681744
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 960681740}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &960681745
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 960681740}
  m_CullTransparentMesh: 0
--- !u!1 &972651476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 972651478}
  - component: {fileID: 972651477}
  m_Layer: 0
  m_Name: Rewired Input Manager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &972651477
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972651476}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 581b3e3c96df0ef42aef16b63b27b87f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _dontDestroyOnLoad: 1
  _userData:
    configVars:
      updateMode: 0
      updateLoop: 3
      alwaysUseUnityInput: 0
      windowsStandalonePrimaryInputSource: 0
      osx_primaryInputSource: 0
      linux_primaryInputSource: 0
      windowsUWP_primaryInputSource: 0
      xboxOne_primaryInputSource: 0
      gameCoreXboxOne_primaryInputSource: 0
      gameCoreScarlett_primaryInputSource: 0
      ps4_primaryInputSource: 0
      ps5_primaryInputSource: 0
      webGL_primaryInputSource: 0
      stadia_primaryInputSource: 0
      useXInput: 1
      useNativeMouse: 1
      useEnhancedDeviceSupport: 1
      osxStandalone_useEnhancedDeviceSupport: 1
      android_supportUnknownGamepads: 0
      ps4_assignJoysticksByPS4JoyId: 1
      useSteamControllerSupport: 1
      logToScreen: 0
      runInEditMode: 0
      allowInputInEditorSceneView: 0
      unityUsePhysicalKeys: 1
      keyCombinationOverrideMode: 1
      generateKeyEventsOnKeyCombinationOverride: 0
      platformVars_windowsStandalone:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        useNativeKeyboard: 1
        joystickRefreshRate: 240
        useWindowsGamingInput: 0
        enhancedDeviceSupportExcludedDeviceTypes: 
      platformVars_linuxStandalone:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        useEnhancedDeviceSupport: 1
        enhancedDeviceSupportExcludedDeviceTypes: 
      platformVars_osxStandalone:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        useAppleGameController: 0
        assignJoysticksByUserId: 0
        enhancedDeviceSupportExcludedDeviceTypes: 
      platformVars_windowsUWP:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        useGamepadAPI: 1
        useHIDAPI: 1
      platformVars_iOS:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_tvOS:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_android:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_ps4:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_ps5:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        assignJoysticksByPS5JoyId: 1
      platformVars_psVita:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_xboxOne:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_gameCoreXboxOne:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        assignJoysticksByUserId: 0
      platformVars_gameCoreScarlett:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        assignJoysticksByUserId: 0
      platformVars_switch:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_webGL:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
      platformVars_stadia:
        disableKeyboard: 0
        disableMouse: 0
        ignoreInputWhenAppNotInFocus: 1
        useNativeKeyboard: 1
        useNativeMouse: 1
      maxJoysticksPerPlayer: 1
      autoAssignJoysticks: 1
      assignJoysticksToPlayingPlayersOnly: 0
      distributeJoysticksEvenly: 1
      reassignJoystickToPreviousOwnerOnReconnect: 1
      defaultJoystickAxis2DDeadZoneType: 1
      defaultJoystickAxis2DSensitivityType: 0
      defaultAxisSensitivityType: 0
      force4WayHats: 0
      throttleCalibrationMode: 0
      activateActionButtonsOnNegativeValue: 0
      deferControllerConnectedEventsOnStart: 0
      logLevel: 7
      editorSettings:
        exportConsts_useParentClass: 0
        exportConsts_parentClassName: RewiredConsts
        exportConsts_useNamespace: 1
        exportConsts_namespace: RewiredConsts
        exportConsts_actions: 1
        exportConsts_actionsClassName: Action
        exportConsts_actionsIncludeActionCategory: 0
        exportConsts_actionsCreateClassesForActionCategories: 0
        exportConsts_mapCategories: 1
        exportConsts_mapCategoriesClassName: Category
        exportConsts_layouts: 1
        exportConsts_layoutsClassName: Layout
        exportConsts_players: 1
        exportConsts_playersClassName: Player
        exportConsts_inputBehaviors: 0
        exportConsts_inputBehaviorsClassName: InputBehavior
        exportConsts_customControllers: 1
        exportConsts_customControllersClassName: CustomController
        exportConsts_customControllersAxesClassName: Axis
        exportConsts_customControllersButtonsClassName: Button
        exportConsts_layoutManagerRuleSets: 1
        exportConsts_layoutManagerRuleSetsClassName: LayoutManagerRuleSet
        exportConsts_mapEnablerRuleSets: 1
        exportConsts_mapEnablerRuleSetsClassName: MapEnablerRuleSet
        exportConsts_allCapsConstantNames: 0
    players:
    - _id: 9999999
      _name: System
      _descriptiveName: System
      _startPlaying: 1
      _defaultJoystickMaps: []
      _defaultMouseMaps: []
      _defaultKeyboardMaps: []
      _defaultCustomControllerMaps: []
      _startingCustomControllers: []
      _assignMouseOnStart: 1
      _assignKeyboardOnStart: 1
      _excludeFromControllerAutoAssignment: 1
      _controllerMapLayoutManagerSettings:
        _enabled: 1
        _loadFromUserDataStore: 1
        _ruleSets: []
      _controllerMapEnablerSettings:
        _enabled: 1
        _ruleSets: []
    - _id: 1
      _name: Player1
      _descriptiveName: Player 1
      _startPlaying: 1
      _defaultJoystickMaps: []
      _defaultMouseMaps: []
      _defaultKeyboardMaps: []
      _defaultCustomControllerMaps:
      - _enabled: 1
        _categoryId: 0
        _layoutId: 0
      _startingCustomControllers:
      - _sourceId: 0
        _tag: main
      _assignMouseOnStart: 0
      _assignKeyboardOnStart: 1
      _excludeFromControllerAutoAssignment: 0
      _controllerMapLayoutManagerSettings:
        _enabled: 1
        _loadFromUserDataStore: 1
        _ruleSets: []
      _controllerMapEnablerSettings:
        _enabled: 1
        _ruleSets: []
    - _id: 2
      _name: Player2
      _descriptiveName: Player 2
      _startPlaying: 1
      _defaultJoystickMaps: []
      _defaultMouseMaps: []
      _defaultKeyboardMaps: []
      _defaultCustomControllerMaps:
      - _enabled: 1
        _categoryId: 0
        _layoutId: 0
      _startingCustomControllers:
      - _sourceId: 0
        _tag: main
      _assignMouseOnStart: 0
      _assignKeyboardOnStart: 1
      _excludeFromControllerAutoAssignment: 0
      _controllerMapLayoutManagerSettings:
        _enabled: 1
        _loadFromUserDataStore: 1
        _ruleSets: []
      _controllerMapEnablerSettings:
        _enabled: 1
        _ruleSets: []
    actions:
    - _id: 0
      _name: Move Horizontal
      _type: 0
      _descriptiveName: Move Horizontal
      _positiveDescriptiveName: Move Right
      _negativeDescriptiveName: Move Left
      _behaviorId: 0
      _userAssignable: 1
      _categoryId: 0
    - _id: 1
      _name: Move Vertical
      _type: 0
      _descriptiveName: Move Vertical
      _positiveDescriptiveName: Move Up
      _negativeDescriptiveName: Move Down
      _behaviorId: 0
      _userAssignable: 1
      _categoryId: 0
    - _id: 2
      _name: WeaponSwitchUp
      _type: 1
      _descriptiveName: WeaponSwitchUp
      _positiveDescriptiveName: 
      _negativeDescriptiveName: 
      _behaviorId: 0
      _userAssignable: 1
      _categoryId: 0
    - _id: 3
      _name: WeaponSwitchDown
      _type: 1
      _descriptiveName: WeaponSwitchDown
      _positiveDescriptiveName: 
      _negativeDescriptiveName: 
      _behaviorId: 0
      _userAssignable: 1
      _categoryId: 0
    actionCategories:
    - _name: Default
      _descriptiveName: Default
      _tag: 
      _id: 0
      _userAssignable: 1
    actionCategoryMap:
      list:
      - categoryId: 0
        actionIds: 00000000010000000200000003000000
    inputBehaviors:
    - _id: 0
      _name: Default
      _joystickAxisSensitivity: 1
      _digitalAxisSimulation: 1
      _digitalAxisSnap: 1
      _digitalAxisInstantReverse: 0
      _digitalAxisGravity: 3
      _digitalAxisSensitivity: 3
      _mouseXYAxisMode: 0
      _mouseOtherAxisMode: 0
      _mouseXYAxisSensitivity: 1
      _mouseXYAxisDeltaCalc: 0
      _mouseOtherAxisSensitivity: 1
      _customControllerAxisSensitivity: 1
      _buttonDoublePressSpeed: 0.3
      _buttonShortPressTime: 0.25
      _buttonShortPressExpiresIn: 0
      _buttonLongPressTime: 1
      _buttonLongPressExpiresIn: 0
      _buttonDeadZone: 0.5
      _buttonDownBuffer: 0
      _buttonRepeatRate: 30
      _buttonRepeatDelay: 0
    mapCategories:
    - _name: Default
      _descriptiveName: Default
      _tag: 
      _id: 0
      _userAssignable: 1
      _checkConflictsWithAllCategories: 1
      _checkConflictsCategoryIds: 
    joystickLayouts:
    - _name: Default
      _descriptiveName: Default
      _id: 0
    keyboardLayouts:
    - _name: Default
      _descriptiveName: Default
      _id: 0
    mouseLayouts:
    - _name: Default
      _descriptiveName: Default
      _id: 0
    customControllerLayouts:
    - _name: Default
      _descriptiveName: Default
      _id: 0
    joystickMaps: []
    keyboardMaps: []
    mouseMaps: []
    customControllerMaps:
    - id: 0
      categoryId: 0
      layoutId: 0
      name: 
      hardwareGuidString: 
      customControllerUid: 0
      actionElementMaps:
      - _actionCategoryId: 0
        _actionId: 0
        _elementType: 0
        _elementIdentifierId: 0
        _axisRange: 0
        _invert: 0
        _axisContribution: 0
        _keyboardKeyCode: 0
        _modifierKey1: 0
        _modifierKey2: 0
        _modifierKey3: 0
      - _actionCategoryId: 0
        _actionId: 1
        _elementType: 0
        _elementIdentifierId: 1
        _axisRange: 0
        _invert: 0
        _axisContribution: 0
        _keyboardKeyCode: 0
        _modifierKey1: 0
        _modifierKey2: 0
        _modifierKey3: 0
      - _actionCategoryId: 0
        _actionId: 2
        _elementType: 1
        _elementIdentifierId: 3
        _axisRange: 0
        _invert: 0
        _axisContribution: 0
        _keyboardKeyCode: 0
        _modifierKey1: 0
        _modifierKey2: 0
        _modifierKey3: 0
      - _actionCategoryId: 0
        _actionId: 3
        _elementType: 1
        _elementIdentifierId: 2
        _axisRange: 0
        _invert: 0
        _axisContribution: 0
        _keyboardKeyCode: 0
        _modifierKey1: 0
        _modifierKey2: 0
        _modifierKey3: 0
    customControllers:
    - _name: TouchController
      _descriptiveName: TouchController
      _id: 0
      _typeGuidString: a65c3509-4a97-4c32-a2e5-139d48a4bca3
      _elementIdentifiers:
      - _id: 0
        _name: Stick X
        _positiveName: 
        _negativeName: 
        _elementType: 0
        _compoundElementType: 0
      - _id: 1
        _name: Stick Y
        _positiveName: 
        _negativeName: 
        _elementType: 0
        _compoundElementType: 0
      - _id: 2
        _name: WeaponSwitchDown
        _positiveName: 
        _negativeName: 
        _elementType: 1
        _compoundElementType: 0
      - _id: 3
        _name: WeaponSwitchUp
        _positiveName: 
        _negativeName: 
        _elementType: 1
        _compoundElementType: 0
      _axes:
      - elementIdentifierId: 0
        name: Stick X
        range: 0
        invert: 0
        deadZone: 0
        zero: 0
        min: -1
        max: 1
        doNotCalibrateRange: 0
        sensitivityType: 0
        sensitivity: 1
        sensitivityCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        axisInfo:
          _dataFormat: 0
          _excludeFromPolling: 0
          _specialAxisType: 0
          _pollingDeadZone: -1
      - elementIdentifierId: 1
        name: Stick Y
        range: 0
        invert: 0
        deadZone: 0
        zero: 0
        min: -1
        max: 1
        doNotCalibrateRange: 0
        sensitivityType: 0
        sensitivity: 1
        sensitivityCurve:
          serializedVersion: 2
          m_Curve: []
          m_PreInfinity: 2
          m_PostInfinity: 2
          m_RotationOrder: 4
        axisInfo:
          _dataFormat: 0
          _excludeFromPolling: 0
          _specialAxisType: 0
          _pollingDeadZone: -1
      _buttons:
      - elementIdentifierId: 3
        name: WeaponSwitchUp
      - elementIdentifierId: 2
        name: WeaponSwitchDown
      _elementIdentifierIdCounter: 6
    controllerMapLayoutManagerRuleSets: []
    controllerMapEnablerRuleSets: []
    playerIdCounter: 3
    actionIdCounter: 5
    actionCategoryIdCounter: 1
    inputBehaviorIdCounter: 1
    mapCategoryIdCounter: 1
    joystickLayoutIdCounter: 1
    keyboardLayoutIdCounter: 1
    mouseLayoutIdCounter: 1
    customControllerLayoutIdCounter: 1
    joystickMapIdCounter: 0
    keyboardMapIdCounter: 0
    mouseMapIdCounter: 0
    customControllerMapIdCounter: 1
    customControllerIdCounter: 1
    controllerMapLayoutManagerSetIdCounter: 0
    controllerMapEnablerSetIdCounter: 0
  _controllerDataFiles: {fileID: 11400000, guid: d21d2b20df8369642b76aafbb2576ba7,
    type: 2}
--- !u!4 &972651478
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 972651476}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &980428978
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 980428982}
  - component: {fileID: 980428981}
  - component: {fileID: 980428980}
  - component: {fileID: 980428979}
  - component: {fileID: 980428983}
  m_Layer: 20
  m_Name: Sphere
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!135 &980428979
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 980428978}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &980428980
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 980428978}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &980428981
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 980428978}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &980428982
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 980428978}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 5.09, y: 1.48, z: 2.41}
  m_LocalScale: {x: 0.4864601, y: 0.4864601, z: 0.4864601}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &980428983
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 980428978}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &987441010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 987441011}
  - component: {fileID: 987441015}
  - component: {fileID: 987441014}
  - component: {fileID: 987441013}
  - component: {fileID: 987441012}
  m_Layer: 0
  m_Name: ButtonX
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &987441011
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 987441010}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 841690213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -883, y: 506}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &987441012
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 987441010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Interactable: 1
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: XPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: XReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: XPressed
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 0.3
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 0
--- !u!114 &987441013
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 987441010}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300008, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &987441014
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 987441010}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &987441015
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 987441010}
  m_CullTransparentMesh: 0
--- !u!1 &992956112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 992956113}
  - component: {fileID: 992956115}
  - component: {fileID: 992956114}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &992956113
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992956112}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1957787061}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 137.25, y: -71.200005}
  m_SizeDelta: {x: 261.91, y: 142.4}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &992956114
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992956112}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 10
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 40
    m_Alignment: 6
    m_AlignByGeometry: 1
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 1
    m_LineSpacing: 1.1
  m_Text: 'Console Log

'
--- !u!222 &992956115
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 992956112}
  m_CullTransparentMesh: 0
--- !u!114 &1015305831 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3387135692686593795, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c977cd4b40934cb887c285ed1f8476e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1015305832 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 6607182855594882500, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96268f2884c13d24eb4c6351a3388a7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1020340877 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7919645375193850435, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1db305b54b5c3934dbcbddbda5d14f5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1033495235
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1033495236}
  - component: {fileID: 1033495239}
  - component: {fileID: 1033495238}
  - component: {fileID: 1033495237}
  m_Layer: 5
  m_Name: Canvas Constant Physical
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1033495236
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033495235}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1297704751}
  - {fileID: 1782685487}
  - {fileID: 250731758}
  m_Father: {fileID: 1706917448}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1033495237
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033495235}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 1048575
--- !u!114 &1033495238
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033495235}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 2
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1033495239
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1033495235}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 0
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1070796639
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1070796642}
  - component: {fileID: 1070796641}
  - component: {fileID: 1070796640}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1070796640
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070796639}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 14
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 10
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: Hello World !
--- !u!222 &1070796641
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070796639}
  m_CullTransparentMesh: 0
--- !u!224 &1070796642
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1070796639}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2112996225}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: -6, y: -2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &1081750592
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 731133087}
    m_Modifications:
    - target: {fileID: 2871507367516815148, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_Name
      value: SpatialGridDebuger
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: a9e61014dc10c5a49b66564b3354e12b, type: 3}
--- !u!4 &1081750593 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 3396987989814594384, guid: a9e61014dc10c5a49b66564b3354e12b,
    type: 3}
  m_PrefabInstance: {fileID: 1081750592}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1086374880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1086374883}
  - component: {fileID: 1086374882}
  - component: {fileID: 1086374881}
  m_Layer: 0
  m_Name: EventSystem
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1086374881
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1086374880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4f231c4fb786f3946a6b90b886c48677, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_SendPointerHoverToParent: 1
  m_HorizontalAxis: Horizontal
  m_VerticalAxis: Vertical
  m_SubmitButton: Submit
  m_CancelButton: Cancel
  m_InputActionsPerSecond: 10
  m_RepeatDelay: 0.5
  m_ForceModuleActive: 0
--- !u!114 &1086374882
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1086374880}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 76c392e42b5098c458856cdf6ecaaaa1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_FirstSelected: {fileID: 0}
  m_sendNavigationEvents: 1
  m_DragThreshold: 10
--- !u!4 &1086374883
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1086374880}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1097088627
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1097088628}
  - component: {fileID: 1097088630}
  - component: {fileID: 1097088629}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1097088628
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1097088627}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1789951446}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 100, y: 100}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1097088629
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1097088627}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 13
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Reset

    Position'
--- !u!222 &1097088630
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1097088627}
  m_CullTransparentMesh: 0
--- !u!1 &1136780204
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1136780205}
  - component: {fileID: 1136780209}
  - component: {fileID: 1136780208}
  - component: {fileID: 1136780207}
  - component: {fileID: 1136780206}
  m_Layer: 0
  m_Name: JoystickRepositionable
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1136780205
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136780204}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 300860919}
  - {fileID: 336932668}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 1399, y: -513}
  m_SizeDelta: {x: 1024, y: 1024}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1136780206
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136780204}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1a60ed48e41f7574f890b8669d6fbfe0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetCamera: {fileID: 0}
  HorizontalAxisEnabled: 1
  VerticalAxisEnabled: 1
  MaxRangeMode: 0
  MaxRange: 1.5
  MaxRangeTransform: {fileID: 0}
  JoystickValue:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: MoreMountains.Tools.MMControlsTestInputManager,
          MoreMountains.Tools
        m_MethodName: RepositionableJoystickMovement
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  JoystickNormalizedValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickMagnitudeValue:
    m_PersistentCalls:
      m_Calls: []
  OnPointerDownEvent:
    m_PersistentCalls:
      m_Calls: []
  OnDragEvent:
    m_PersistentCalls:
      m_Calls: []
  OnPointerUpEvent:
    m_PersistentCalls:
      m_Calls: []
  RotatingIndicator: {fileID: 0}
  RotatingIndicatorThreshold: 0.1
  PressedOpacity: 0.5
  InterpolateOpacity: 1
  InterpolateOpacitySpeed: 1
  RawValue: {x: 0, y: 0}
  NormalizedValue: {x: 0, y: 0}
  Magnitude: 0
  DrawGizmos: 1
  KnobCanvasGroup: {fileID: 336932670}
  BackgroundCanvasGroup: {fileID: 300860921}
  ConstrainToInitialRectangle: 1
  ResetPositionToInitialOnRelease: 0
--- !u!114 &1136780207
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136780204}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.2}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1136780208
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136780204}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1136780209
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1136780204}
  m_CullTransparentMesh: 0
--- !u!1 &1200119513
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1200119514}
  - component: {fileID: 1200119516}
  - component: {fileID: 1200119515}
  m_Layer: 5
  m_Name: Disclaimer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1200119514
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1200119513}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1693928276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 1906.8, y: 76.200195}
  m_SizeDelta: {x: 3753.7, y: 84.5}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1200119515
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1200119513}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 12800000, guid: 5686e06ef14cf104b8e282ee7c41b9a6, type: 3}
    m_FontSize: 50
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 2
    m_MaxSize: 200
    m_Alignment: 6
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: This demo is best viewed with a Game window set to a 1920x1080 ratio.
--- !u!222 &1200119516
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1200119513}
  m_CullTransparentMesh: 0
--- !u!1 &1207134984
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1207134988}
  - component: {fileID: 1207134987}
  - component: {fileID: 1207134986}
  - component: {fileID: 1207134985}
  m_Layer: 0
  m_Name: Plane
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!64 &1207134985
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207134984}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1207134986
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207134984}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 3a783cc400544be43ae23fda83b65420, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1207134987
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207134984}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1207134988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1207134984}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.06003669}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1215299989 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5319643890459908578, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1215299991
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1215299989}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268, w: 0.9867998}
  limit: 35
  twistLimit: 35
--- !u!1 &1228694480
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1228694482}
  - component: {fileID: 1228694481}
  m_Layer: 0
  m_Name: SubScene
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1228694481
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228694480}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45a335734b1572644a6a5d09d87adc65, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _SceneAsset: {fileID: 102900000, guid: 7e44f85e6edb28a4b88caa39856c01d8, type: 3}
  _HierarchyColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  AutoLoadScene: 1
  _SceneGUID:
    Value:
      x: 3851371751
      y: 1250082278
      z: 2477443211
      w: 2366686808
--- !u!4 &1228694482
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1228694480}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1233107367
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1233107368}
  - component: {fileID: 1233107369}
  m_Layer: 0
  m_Name: MMTouchControls
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1233107368
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1233107367}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 367723575}
  - {fileID: 2139117039}
  - {fileID: 841690213}
  - {fileID: 552138590}
  - {fileID: 1914733753}
  - {fileID: 1136780205}
  - {fileID: 715755611}
  - {fileID: 1604868501}
  m_Father: {fileID: 1693928276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1233107369
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1233107367}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!1 &1245264064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1245264065}
  - component: {fileID: 1245264067}
  - component: {fileID: 1245264066}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1245264065
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1245264064}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1693928276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 50, y: -50}
  m_SizeDelta: {x: 540.7159, y: 100.964}
  m_Pivot: {x: 0, y: 1}
--- !u!114 &1245264066
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1245264064}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: New Text
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4294967295
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 72
  m_fontSizeBase: 50
  m_fontWeight: 400
  m_enableAutoSizing: 1
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 1
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1245264067
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1245264064}
  m_CullTransparentMesh: 1
--- !u!1 &1277722379
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1277722380}
  - component: {fileID: 1277722384}
  - component: {fileID: 1277722383}
  - component: {fileID: 1277722382}
  - component: {fileID: 1277722381}
  m_Layer: 0
  m_Name: ButtonB
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1277722380
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1277722379}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 841690213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -405.00024, y: 506}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1277722381
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1277722379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Interactable: 1
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: BPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: BReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: BPressed
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 0.3
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 0
--- !u!114 &1277722382
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1277722379}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300012, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1277722383
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1277722379}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1277722384
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1277722379}
  m_CullTransparentMesh: 0
--- !u!95 &1277964586 stripped
Animator:
  m_CorrespondingSourceObject: {fileID: 7342432587237116706, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1277964587 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 712441798001151660, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1281752709 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 4829969537808851870, guid: 0010ea5588d4a184383249fc0ff264d4,
    type: 3}
  m_PrefabInstance: {fileID: 35558000}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1281752712
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1281752709}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b7b24cf5705a1a946a37d94665250665, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _configuration: {fileID: 11400000, guid: 82160471aa3291c4494239765c64a3c4, type: 2}
--- !u!1 &1297704750
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1297704751}
  - component: {fileID: 1297704753}
  - component: {fileID: 1297704752}
  m_Layer: 5
  m_Name: Intention L
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1297704751
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297704750}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1305468111}
  m_Father: {fileID: 1033495236}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 7.5, y: 0}
  m_SizeDelta: {x: 15, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1297704752
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297704750}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.13333334, g: 0.16862746, b: 0.21568628, a: 0.392}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1297704753
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1297704750}
  m_CullTransparentMesh: 0
--- !u!1 &1305468110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1305468111}
  - component: {fileID: 1305468113}
  - component: {fileID: 1305468112}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1305468111
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305468110}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1297704751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.6199999, y: -0.26000977}
  m_SizeDelta: {x: 147.84, y: -150.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1305468112
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305468110}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 4
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 1
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Intentionally left blank - to prevent accidental touches on thin-bezel
    phone.
--- !u!222 &1305468113
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1305468110}
  m_CullTransparentMesh: 0
--- !u!1001 &1341390262
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 340151032}
    m_Modifications:
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 250.12659
      objectReference: {fileID: 0}
    - target: {fileID: 5159494392815815213, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 5249935509380363135, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: anchorRect.x
      value: 136.52966
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 60.79844
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978294289555, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 98.66407
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978363083901, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 136.5297
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978571195861, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 212.26096
      objectReference: {fileID: 0}
    - target: {fileID: 6714727978668114424, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979177705959, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 22.932814
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979286263155, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 37.865627
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 27.299988
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 174.39532
      objectReference: {fileID: 0}
    - target: {fileID: 6714727979414783924, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -17.649994
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_RootOrder
      value: 6
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 273.06134
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 52.79319
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 187.07
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -200.03
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783061, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8001126802129783062, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: m_Name
      value: Scene Navigator
      objectReference: {fileID: 0}
    - target: {fileID: 9193411078622232481, guid: 2da7ec3bcc0f24a939908d096ac0c351,
        type: 3}
      propertyPath: anchorRect.x
      value: 136.52966
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2da7ec3bcc0f24a939908d096ac0c351, type: 3}
--- !u!1 &1361600900
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1361600901}
  - component: {fileID: 1361600905}
  - component: {fileID: 1361600904}
  - component: {fileID: 1361600903}
  - component: {fileID: 1361600902}
  m_Layer: 0
  m_Name: ButtonA
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1361600901
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361600900}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 841690213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -644.0001, y: 219}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1361600902
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361600900}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Interactable: 1
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: APressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: AReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: APressed
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 0.3
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 0
--- !u!114 &1361600903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361600900}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300002, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1361600904
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361600900}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1361600905
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1361600900}
  m_CullTransparentMesh: 0
--- !u!1 &1370782250 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 5615533298249553943, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!1001 &1407028523
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1706917448}
    m_Modifications:
    - target: {fileID: 987087, guid: fe9566cade1824dbb9bf6045d8260ce9, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 975522766, guid: fe9566cade1824dbb9bf6045d8260ce9, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1942487873, guid: fe9566cade1824dbb9bf6045d8260ce9, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137132445267, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: msg
      value: 
      objectReference: {fileID: 2112996224}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: inputMove
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: SkillCanceller
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: skillCanceller
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: 'skillButtons.Array.data[0]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: 'skillButtons.Array.data[1]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: 'skillButtons.Array.data[2]'
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_RootOrder
      value: 3
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236879, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_Name
      value: ExampleActor
      objectReference: {fileID: 0}
    - target: {fileID: 5642459137347236879, guid: fe9566cade1824dbb9bf6045d8260ce9,
        type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: fe9566cade1824dbb9bf6045d8260ce9, type: 3}
--- !u!114 &1454374007 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 3336923530583100265, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370782250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1895a8d88d74273ba2e4fb0324e66e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1454374011 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 9167802454622032298, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370782250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8cfa865e8923b45b97f2e6e5d21ce9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1454374016 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5887728694554286191, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370782250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0816b76efdaf0f8499691c5103211fc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &1454374025
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1370782250}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2cacf59aa803a8d418b9c9d6f3aadc13, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  enableCentralizedRotation: 1
  currentPriority: 0
  maxRotationSpeed: 180
  rotationSmoothTime: 0.2
  targetSwitchCooldown: 0.3
  proximityThreshold: 2.78
  enableDebugLogging: 0
  showDebugGizmos: 1
  showDebugUI: 1
  logRotationRequests: 1
  logRotationConflicts: 1
  playerTransform: {fileID: 832293011}
  movementModule: {fileID: 1015305832}
  upperBodyController: {fileID: 0}
  ragdollAiming: {fileID: 603274978}
--- !u!1001 &1494654533
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1448112055169428, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_Name
      value: 3DCameras --------------------------------------------------------
      objectReference: {fileID: 0}
    - target: {fileID: 1448112055169428, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_IsActive
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1708842091693502, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_IsActive
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 9.77
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.y
      value: 4.28
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.z
      value: 11.5
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4199879678763332, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.8538685
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.y
      value: 14.866781
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.z
      value: -12.672259
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.67270243
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.33174005
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.59317136
      objectReference: {fileID: 0}
    - target: {fileID: 4407977586509776, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.29251966
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.x
      value: 1.8538685
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.y
      value: 14.866781
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalPosition.z
      value: -12.672259
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.w
      value: 0.67270243
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.33174005
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.59317136
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0.29251966
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 52.5
      objectReference: {fileID: 0}
    - target: {fileID: 4796342324616292, guid: d4529f47989526241929319abfdfc29d, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: -82.81
      objectReference: {fileID: 0}
    - target: {fileID: 20055413374703424, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: field of view
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 20055413374703424, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_FocalLength
      value: 32.96973
      objectReference: {fileID: 0}
    - target: {fileID: 20055413374703424, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_CullingMask.m_Bits
      value: 4294967295
      objectReference: {fileID: 0}
    - target: {fileID: 20055413374703424, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_projectionMatrixMode
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 114893981776735634, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_Follow
      value: 
      objectReference: {fileID: 832293011}
    - target: {fileID: 114893981776735634, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_LookAt
      value: 
      objectReference: {fileID: 832293011}
    - target: {fileID: 114893981776735634, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_Lens.FieldOfView
      value: 30
      objectReference: {fileID: 0}
    - target: {fileID: 114947792213459908, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_ScreenX
      value: 0.476
      objectReference: {fileID: 0}
    - target: {fileID: 114947792213459908, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_ScreenY
      value: 0.7
      objectReference: {fileID: 0}
    - target: {fileID: 114947792213459908, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_LookaheadTime
      value: 0.133
      objectReference: {fileID: 0}
    - target: {fileID: 114947792213459908, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_CameraDistance
      value: 22.3
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224007289121918876, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 133
      objectReference: {fileID: 0}
    - target: {fileID: 224016105755652760, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 271
      objectReference: {fileID: 0}
    - target: {fileID: 224121795855352212, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 340
      objectReference: {fileID: 0}
    - target: {fileID: 224142829877349906, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 202
      objectReference: {fileID: 0}
    - target: {fileID: 224168492877069080, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224175281271799354, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 409
      objectReference: {fileID: 0}
    - target: {fileID: 224193922176695934, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 202
      objectReference: {fileID: 0}
    - target: {fileID: 224199244831142444, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 133
      objectReference: {fileID: 0}
    - target: {fileID: 224200583791472412, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 133
      objectReference: {fileID: 0}
    - target: {fileID: 224244000298074872, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 133
      objectReference: {fileID: 0}
    - target: {fileID: 224254690500600484, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 340
      objectReference: {fileID: 0}
    - target: {fileID: 224269632069082960, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 271
      objectReference: {fileID: 0}
    - target: {fileID: 224394418919511126, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224453468194692066, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 340
      objectReference: {fileID: 0}
    - target: {fileID: 224489653801959224, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224513189340714208, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 271
      objectReference: {fileID: 0}
    - target: {fileID: 224533305164393540, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 202
      objectReference: {fileID: 0}
    - target: {fileID: 224541603227158946, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224552186883157064, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 409
      objectReference: {fileID: 0}
    - target: {fileID: 224593376806033862, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -202
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 340
      objectReference: {fileID: 0}
    - target: {fileID: 224681683108154520, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 409
      objectReference: {fileID: 0}
    - target: {fileID: 224772212812985076, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -64
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 202
      objectReference: {fileID: 0}
    - target: {fileID: 224928906037718640, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 271
      objectReference: {fileID: 0}
    - target: {fileID: 224940472473957458, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -271
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 64
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 409
      objectReference: {fileID: 0}
    - target: {fileID: 224992182871114304, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: -133
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 114015822488491818, guid: d4529f47989526241929319abfdfc29d, type: 3}
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 1451566859140498, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 1494654535}
    - targetCorrespondingSourceObject: {fileID: 1708842091693502, guid: d4529f47989526241929319abfdfc29d,
        type: 3}
      insertIndex: -1
      addedObject: {fileID: 335063809}
  m_SourcePrefab: {fileID: 100100000, guid: d4529f47989526241929319abfdfc29d, type: 3}
--- !u!1 &1494654534 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 1451566859140498, guid: d4529f47989526241929319abfdfc29d,
    type: 3}
  m_PrefabInstance: {fileID: 1494654533}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1494654535
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1494654534}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a79441f348de89743a2939f4d699eac1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_RenderShadows: 1
  m_RequiresDepthTextureOption: 2
  m_RequiresOpaqueTextureOption: 2
  m_CameraType: 0
  m_Cameras: []
  m_RendererIndex: 0
  m_VolumeLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_VolumeTrigger: {fileID: 0}
  m_VolumeFrameworkUpdateModeOption: 2
  m_RenderPostProcessing: 0
  m_Antialiasing: 0
  m_AntialiasingQuality: 2
  m_StopNaN: 1
  m_Dithering: 0
  m_ClearDepth: 1
  m_AllowXRRendering: 1
  m_AllowHDROutput: 1
  m_UseScreenCoordOverride: 0
  m_ScreenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  m_ScreenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  m_RequiresDepthTexture: 0
  m_RequiresColorTexture: 0
  m_Version: 2
  m_TaaSettings:
    m_Quality: 3
    m_FrameInfluence: 0.1
    m_JitterScale: 1
    m_MipBias: 0
    m_VarianceClampScale: 0.9
    m_ContrastAdaptiveSharpening: 0
--- !u!20 &1494654542 stripped
Camera:
  m_CorrespondingSourceObject: {fileID: 20055413374703424, guid: d4529f47989526241929319abfdfc29d,
    type: 3}
  m_PrefabInstance: {fileID: 1494654533}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &1507738686
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1507738687}
  - component: {fileID: 1507738689}
  - component: {fileID: 1507738688}
  m_Layer: 5
  m_Name: Logo
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1507738687
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1507738686}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 465214845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0.2, y: 1}
  m_AnchoredPosition: {x: -0.21398926, y: 0.000076293945}
  m_SizeDelta: {x: -0.008010864, y: -0.000061035156}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1507738688
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1507738686}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.70980394}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: a955556141d44b34583685efc505595f, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1507738689
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1507738686}
  m_CullTransparentMesh: 0
--- !u!114 &1513624735 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7142898169479841738, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 583b0b8f3bcc4bf6b0e1be30c2aa1e2c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1539814065
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1539814066}
  - component: {fileID: 1539814068}
  - component: {fileID: 1539814067}
  m_Layer: 5
  m_Name: Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1539814066
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539814065}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.0062480997, y: 0.0062480997, z: 0.0062480997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1857771162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 6.69, y: 4.32}
  m_SizeDelta: {x: 747.873, y: 345.561}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1539814067
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539814065}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.4339623, g: 0.4339623, b: 0.4339623, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 50
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 70
    m_Alignment: 4
    m_AlignByGeometry: 1
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Drop aiming on Cancel button

    To cancel skill aiming'
--- !u!222 &1539814068
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1539814065}
  m_CullTransparentMesh: 0
--- !u!1 &1582356326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1582356331}
  - component: {fileID: 1582356330}
  - component: {fileID: 1582356329}
  - component: {fileID: 1582356328}
  - component: {fileID: 1582356327}
  m_Layer: 20
  m_Name: Sphere (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!54 &1582356327
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582356326}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!135 &1582356328
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582356326}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1582356329
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582356326}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1582356330
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582356326}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1582356331
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1582356326}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.09, y: 1.48, z: 0.59}
  m_LocalScale: {x: 0.4864601, y: 0.4864601, z: 0.4864601}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1584897382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1584897383}
  - component: {fileID: 1584897387}
  - component: {fileID: 1584897386}
  - component: {fileID: 1584897385}
  - component: {fileID: 1584897384}
  m_Layer: 0
  m_Name: ButtonRT
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1584897383
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584897382}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 841690213}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -270.00012, y: 848}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1584897384
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584897382}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 87a286771fa86194e979f187a1691a2a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  Interactable: 1
  ButtonPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: RTPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: RTReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  ButtonPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: RTPressed
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  DisabledSprite: {fileID: 0}
  DisabledChangeColor: 0
  DisabledColor: {r: 1, g: 1, b: 1, a: 1}
  PressedSprite: {fileID: 0}
  PressedChangeColor: 0
  PressedColor: {r: 1, g: 1, b: 1, a: 1}
  HighlightedSprite: {fileID: 0}
  HighlightedChangeColor: 0
  HighlightedColor: {r: 1, g: 1, b: 1, a: 1}
  PressedOpacity: 0.3
  IdleOpacity: 1
  DisabledOpacity: 1
  PressedFirstTimeDelay: 0
  ReleasedDelay: 0
  BufferDuration: 0
  Animator: {fileID: 0}
  IdleAnimationParameterName: Idle
  DisabledAnimationParameterName: Disabled
  PressedAnimationParameterName: Pressed
  MouseMode: 0
--- !u!114 &1584897385
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584897382}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300004, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1584897386
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584897382}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1584897387
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1584897382}
  m_CullTransparentMesh: 0
--- !u!1 &1604868499
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1604868501}
  - component: {fileID: 1604868504}
  - component: {fileID: 1604868503}
  - component: {fileID: 1604868502}
  - component: {fileID: 1604868500}
  m_Layer: 0
  m_Name: JoystickRepositionable (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1604868500
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604868499}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1a60ed48e41f7574f890b8669d6fbfe0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  TargetCamera: {fileID: 1494654542}
  HorizontalAxisEnabled: 1
  VerticalAxisEnabled: 1
  MaxRangeMode: 0
  MaxRange: 207
  MaxRangeTransform: {fileID: 0}
  JoystickValue:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: SetRawInput
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  JoystickNormalizedValue:
    m_PersistentCalls:
      m_Calls: []
  JoystickMagnitudeValue:
    m_PersistentCalls:
      m_Calls: []
  OnPointerDownEvent:
    m_PersistentCalls:
      m_Calls: []
  OnDragEvent:
    m_PersistentCalls:
      m_Calls: []
  OnPointerUpEvent:
    m_PersistentCalls:
      m_Calls: []
  RotatingIndicator: {fileID: 0}
  RotatingIndicatorThreshold: 0.1
  PressedOpacity: 0.5
  InterpolateOpacity: 1
  InterpolateOpacitySpeed: 1
  RawValue: {x: 0, y: 0}
  NormalizedValue: {x: 0, y: 0}
  Magnitude: 0
  DrawGizmos: 1
  KnobCanvasGroup: {fileID: 229625704}
  BackgroundCanvasGroup: {fileID: 1761537831}
  ConstrainToInitialRectangle: 1
  ResetPositionToInitialOnRelease: 0
--- !u!224 &1604868501
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604868499}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1761537834}
  - {fileID: 229625701}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -92.52, y: -669.16}
  m_SizeDelta: {x: 3000, y: 3414.0464}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1604868502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604868499}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1604868503
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604868499}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1604868504
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1604868499}
  m_CullTransparentMesh: 0
--- !u!4 &1652105380 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 5642459137347236867, guid: fe9566cade1824dbb9bf6045d8260ce9,
    type: 3}
  m_PrefabInstance: {fileID: 1407028523}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1652105381 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 5642459137347236864, guid: fe9566cade1824dbb9bf6045d8260ce9,
    type: 3}
  m_PrefabInstance: {fileID: 1407028523}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dfb460616b5b747cca4de55c5cc7f42b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1693928275
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1693928276}
  - component: {fileID: 1693928279}
  - component: {fileID: 1693928278}
  - component: {fileID: 1693928277}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1693928276
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693928275}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0, y: 0, z: 0}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1233107368}
  - {fileID: 1200119514}
  - {fileID: 1245264065}
  - {fileID: 847582824}
  - {fileID: 380025921}
  m_Father: {fileID: 205932704}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0, y: 0}
--- !u!114 &1693928277
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693928275}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 0
--- !u!114 &1693928278
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693928275}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 1
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 1920, y: 1080}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 0
--- !u!223 &1693928279
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1693928275}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 0
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 688246547
  m_SortingOrder: 1
  m_TargetDisplay: 0
--- !u!1 &1706917447
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1706917448}
  m_Layer: 0
  m_Name: Example
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1706917448
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1706917447}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2140445770}
  - {fileID: 1652105380}
  - {fileID: 1857771162}
  - {fileID: 340151032}
  - {fileID: 1033495236}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1712231943 stripped
MonoBehaviour:
  m_CorrespondingSourceObject: {fileID: 7916506578888296180, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaf09bfab896407fa91b5c272c57cb0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1731716459
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1731716460}
  - component: {fileID: 1731716462}
  - component: {fileID: 1731716461}
  m_Layer: 5
  m_Name: Text (TMP)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1731716460
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1731716459}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 380025921}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1731716461
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1731716459}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: Button
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: 4281479730
  m_fontColor: {r: 0.19607843, g: 0.19607843, b: 0.19607843, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: 4294967295
  m_fontSize: 24
  m_fontSizeBase: 24
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_TextWrappingMode: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 0
  m_ActiveFontFeatures: 6e72656b
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_EmojiFallbackSupport: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!222 &1731716462
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1731716459}
  m_CullTransparentMesh: 1
--- !u!1 &1748571130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1748571131}
  - component: {fileID: 1748571134}
  - component: {fileID: 1748571133}
  - component: {fileID: 1748571132}
  m_Layer: 0
  m_Name: JoystickRBackground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1748571131
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748571130}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 367723575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: -25.38, y: 2}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1748571132
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748571130}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300010, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &1748571133
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748571130}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1748571134
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1748571130}
  m_CullTransparentMesh: 0
--- !u!1 &1750936747
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1750936748}
  - component: {fileID: 1750936750}
  - component: {fileID: 1750936749}
  m_Layer: 5
  m_Name: Text (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1750936748
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750936747}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: -0}
  m_LocalScale: {x: 0.0062480997, y: 0.0062480997, z: 0.0062480997}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1857771162}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 2.53, y: 0.35}
  m_SizeDelta: {x: 747.873, y: 345.561}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1750936749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750936747}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.4339623, g: 0.4339623, b: 0.4339623, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 64
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 70
    m_Alignment: 4
    m_AlignByGeometry: 1
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: 'Press and drag

    To aim skill'
--- !u!222 &1750936750
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1750936747}
  m_CullTransparentMesh: 0
--- !u!1 &1761537830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1761537834}
  - component: {fileID: 1761537833}
  - component: {fileID: 1761537831}
  - component: {fileID: 1761537832}
  m_Layer: 0
  m_Name: JoystickRBackground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!225 &1761537831
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761537830}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!114 &1761537832
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761537830}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.29803923}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 4520387000713905209, guid: f3ee7d861c6ac7e4a975285391fe761c,
    type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1761537833
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761537830}
  m_CullTransparentMesh: 0
--- !u!224 &1761537834
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1761537830}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 314017321}
  m_Father: {fileID: 1604868501}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 64.10962, y: -416.32062}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &1773526669
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 177638, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_Name
      value: '[Graphy]'
      objectReference: {fileID: 0}
    - target: {fileID: 11480938, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_debugPackets.Array.data[0].DebugConditions.Array.data[0].Value
      value: 120
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_Pivot.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_Pivot.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchorMax.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchorMax.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 22451668, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 0abab5bb77339e4428787a870eb31bd3, type: 3}
--- !u!1 &1782685486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1782685487}
  - component: {fileID: 1782685489}
  - component: {fileID: 1782685488}
  m_Layer: 5
  m_Name: Intention R
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1782685487
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782685486}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1876159611}
  m_Father: {fileID: 1033495236}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -7.5, y: 0}
  m_SizeDelta: {x: 15, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1782685488
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782685486}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.13333334, g: 0.16862746, b: 0.21568628, a: 0.392}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1782685489
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1782685486}
  m_CullTransparentMesh: 0
--- !u!1 &1789951445
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1789951446}
  - component: {fileID: 1789951449}
  - component: {fileID: 1789951448}
  - component: {fileID: 1789951447}
  m_Layer: 5
  m_Name: Button
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1789951446
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1789951445}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1097088628}
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 293.31934, y: -136.21973}
  m_SizeDelta: {x: 60.56, y: 54.48}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1789951447
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1789951445}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1789951448}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 1652105381}
        m_TargetAssemblyTypeName: 
        m_MethodName: ResetPosition
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1789951448
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1789951445}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 0, b: 0.20549631, a: 0.6313726}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10905, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1789951449
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1789951445}
  m_CullTransparentMesh: 0
--- !u!1 &1822868572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1822868573}
  - component: {fileID: 1822868576}
  - component: {fileID: 1822868575}
  - component: {fileID: 1822868574}
  m_Layer: 5
  m_Name: Asset Store
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1822868573
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822868572}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 155, y: -136.98}
  m_SizeDelta: {x: 209.59998, y: 73.3}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1822868574
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822868572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4e29b1a8efbd4b44bb3f3716e73f07ff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Navigation:
    m_Mode: 3
    m_WrapAround: 0
    m_SelectOnUp: {fileID: 0}
    m_SelectOnDown: {fileID: 0}
    m_SelectOnLeft: {fileID: 0}
    m_SelectOnRight: {fileID: 0}
  m_Transition: 1
  m_Colors:
    m_NormalColor: {r: 1, g: 1, b: 1, a: 1}
    m_HighlightedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_PressedColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 1}
    m_SelectedColor: {r: 0.9607843, g: 0.9607843, b: 0.9607843, a: 1}
    m_DisabledColor: {r: 0.78431374, g: 0.78431374, b: 0.78431374, a: 0.5019608}
    m_ColorMultiplier: 1
    m_FadeDuration: 0.1
  m_SpriteState:
    m_HighlightedSprite: {fileID: 0}
    m_PressedSprite: {fileID: 0}
    m_SelectedSprite: {fileID: 0}
    m_DisabledSprite: {fileID: 0}
  m_AnimationTriggers:
    m_NormalTrigger: Normal
    m_HighlightedTrigger: Highlighted
    m_PressedTrigger: Pressed
    m_SelectedTrigger: Selected
    m_DisabledTrigger: Disabled
  m_Interactable: 1
  m_TargetGraphic: {fileID: 1822868575}
  m_OnClick:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 340151033}
        m_TargetAssemblyTypeName: 
        m_MethodName: OpenAssetStore
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
--- !u!114 &1822868575
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822868572}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300000, guid: 3c2b8d94d617f4043a6d53784cb03700, type: 3}
  m_Type: 0
  m_PreserveAspect: 1
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1822868576
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1822868572}
  m_CullTransparentMesh: 0
--- !u!1 &1832267739
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1832267740}
  m_Layer: 0
  m_Name: BackgroundConstraintRectTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1832267740
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1832267739}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 367723575}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -5.3999023, y: -13.699951}
  m_SizeDelta: {x: -210.9, y: -263.7}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!1001 &1846172000
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 1033495236}
    m_Modifications:
    - target: {fileID: 3289970441668210714, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_Name
      value: TouchesDebugger
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_Pivot.x
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_Pivot.y
      value: 0.5
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_RootOrder
      value: 2
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchorMax.x
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchorMax.y
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchorMin.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchorMin.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_SizeDelta.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_SizeDelta.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalScale.z
      value: 0.91644
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchoredPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_AnchoredPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 3289970441668210717, guid: 964a17ce4461cf24185648fc5fbf9633,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 964a17ce4461cf24185648fc5fbf9633, type: 3}
--- !u!1 &1857699945 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 7195047958775627785, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &1857699947
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857699945}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
  limit: 5
  twistLimit: 5
--- !u!1 &1857771161
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1857771162}
  - component: {fileID: 1857771165}
  - component: {fileID: 1857771164}
  - component: {fileID: 1857771163}
  m_Layer: 5
  m_Name: Canvas (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1857771162
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857771161}
  m_LocalRotation: {x: 0.70710707, y: -0, z: -0, w: 0.70710653}
  m_LocalPosition: {x: 0, y: 0, z: -2.825}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1750936748}
  - {fileID: 1539814066}
  m_Father: {fileID: 1706917448}
  m_LocalEulerAnglesHint: {x: 90.00001, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0.01}
  m_SizeDelta: {x: 21.845, y: 21.931}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1857771163
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857771161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: 1048575
--- !u!114 &1857771164
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857771161}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!223 &1857771165
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1857771161}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_VertexColorAlwaysGammaSpace: 0
  m_AdditionalShaderChannelsFlag: 25
  m_UpdateRectTransformForStandalone: 0
  m_SortingLayerID: 0
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!1 &1876159610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1876159611}
  - component: {fileID: 1876159613}
  - component: {fileID: 1876159612}
  m_Layer: 5
  m_Name: Text
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1876159611
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876159610}
  m_LocalRotation: {x: 0, y: 0, z: 0.7071068, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1782685487}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 90}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: -0.6199951, y: -0.26000977}
  m_SizeDelta: {x: 147.84, y: -150.2}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1876159612
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876159610}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 4
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 0
    m_MaxSize: 40
    m_Alignment: 4
    m_AlignByGeometry: 1
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 1
    m_LineSpacing: 1
  m_Text: Intentionally left blank - to prevent accidental touches on thin-bezel
    phone.
--- !u!222 &1876159613
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1876159610}
  m_CullTransparentMesh: 0
--- !u!1 &1914733752
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1914733753}
  - component: {fileID: 1914733755}
  - component: {fileID: 1914733754}
  m_Layer: 5
  m_Name: Arrows
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &1914733753
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914733752}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 4, y: 4, z: 4}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 871102700}
  - {fileID: 2147199036}
  - {fileID: 417140644}
  - {fileID: 621580495}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 440, y: -512}
  m_SizeDelta: {x: 211.79999, y: 254.49252}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!225 &1914733754
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914733752}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &1914733755
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1914733752}
  m_CullTransparentMesh: 0
--- !u!1 &1941190958
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1941190959}
  - component: {fileID: 1941190961}
  - component: {fileID: 1941190960}
  m_Layer: 5
  m_Name: SwipeZoneTest
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1941190959
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1941190958}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1.00008, y: 1.00008, z: 1.00008}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 715755611}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 1, y: 1}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 0, y: 0}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1941190960
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1941190958}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f7201a12d95ffc409449d95f23cf332, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 0.5019608}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_FontData:
    m_Font: {fileID: 10102, guid: 0000000000000000e000000000000000, type: 0}
    m_FontSize: 100
    m_FontStyle: 0
    m_BestFit: 0
    m_MinSize: 1
    m_MaxSize: 200
    m_Alignment: 4
    m_AlignByGeometry: 0
    m_RichText: 1
    m_HorizontalOverflow: 0
    m_VerticalOverflow: 0
    m_LineSpacing: 1
  m_Text: SWIPE ZONE
--- !u!222 &1941190961
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1941190958}
  m_CullTransparentMesh: 0
--- !u!1 &1957787060
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1957787061}
  - component: {fileID: 1957787065}
  - component: {fileID: 1957787064}
  - component: {fileID: 1957787063}
  - component: {fileID: 1957787062}
  m_Layer: 5
  m_Name: ConsoleLogMCS
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &1957787061
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957787060}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 992956113}
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 187.08, y: -292.19}
  m_SizeDelta: {x: 273.07, y: 142.4}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &1957787062
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957787060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8190baa5b412243af84e344995d2db72, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  txt: {fileID: 992956114}
  cachedText: 
  cachedStrings: []
  lineLimit: 20
  useFilter: 1
  filter: MCS
--- !u!114 &1957787063
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957787060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3312d7739989d2b4e91e6319e9a96d76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Padding: {x: 0, y: 0, z: 0, w: 0}
  m_Softness: {x: 0, y: 0}
--- !u!114 &1957787064
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957787060}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0.13333334, g: 0.16862746, b: 0.21568628, a: 0.5882353}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &1957787065
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1957787060}
  m_CullTransparentMesh: 0
--- !u!1 &1958131078
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1958131080}
  - component: {fileID: 1958131079}
  - component: {fileID: 1958131081}
  m_Layer: 18
  m_Name: Cover
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1958131079
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1958131078}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!4 &1958131080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1958131078}
  serializedVersion: 2
  m_LocalRotation: {x: 0.008271189, y: -0.2303428, z: -0.03491882, w: 0.97244763}
  m_LocalPosition: {x: -1.3692927, y: 0.99, z: 3.66}
  m_LocalScale: {x: 4.0149, y: 2, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 467649621}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: -26.652, z: -4.113}
--- !u!114 &1958131081
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1958131078}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f3bd6cbb53238e742979bf4d5f1fb830, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!1 &1989697414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1989697419}
  - component: {fileID: 1989697418}
  - component: {fileID: 1989697417}
  - component: {fileID: 1989697416}
  - component: {fileID: 1989697415}
  m_Layer: 20
  m_Name: Sphere (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!54 &1989697415
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1989697414}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!135 &1989697416
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1989697414}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.5
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1989697417
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1989697414}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 31321ba15b8f8eb4c954353edc038b1d, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1989697418
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1989697414}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1989697419
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1989697414}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 7.07, y: 1.48, z: 2.41}
  m_LocalScale: {x: 0.4864601, y: 0.4864601, z: 0.4864601}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2020776544 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 6852714991633896669, guid: 73df3382d6e5d1a4a9e6a91c8157ab90,
    type: 3}
  m_PrefabInstance: {fileID: 832293010}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &2020776546
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2020776544}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701, w: 0.9872234}
  limit: 40
  twistLimit: 40
--- !u!1 &2112996223
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2112996225}
  - component: {fileID: 2112996227}
  - component: {fileID: 2112996226}
  - component: {fileID: 2112996224}
  m_Layer: 5
  m_Name: MessageBox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2112996224
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112996223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5f787af4294564195b2cf7fc20d16215, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  cam: {fileID: 0}
  offset: {x: 0, y: 100, z: 0}
  img: {fileID: 2112996226}
  txt: {fileID: 1070796640}
  decayTime: 3
  displayTime: 0
--- !u!224 &2112996225
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112996223}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1070796642}
  m_Father: {fileID: 340151032}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0.000011444, y: 110.399994}
  m_SizeDelta: {x: 165.39, y: 31.940002}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2112996226
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112996223}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 0, g: 0, b: 0, a: 0.5019608}
  m_RaycastTarget: 0
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 10907, guid: 0000000000000000f000000000000000, type: 0}
  m_Type: 1
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!222 &2112996227
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2112996223}
  m_CullTransparentMesh: 0
--- !u!1 &2124832280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2124832282}
  - component: {fileID: 2124832281}
  - component: {fileID: 2124832283}
  m_Layer: 0
  m_Name: GameManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2124832281
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124832280}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b28136b0ddde469d8861aa1268d15f63, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_logEnabled: 0
  m_appTargetFrameRate: -1
  playerController: {fileID: 832293012}
  moduleManager: {fileID: 233670227}
--- !u!4 &2124832282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124832280}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 209.1127, y: -47.387463, z: -176.50934}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 233670228}
  m_Father: {fileID: 855751751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2124832283
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124832280}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: e6c7df3bbc694e4e90218540c4291e49, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  subScene: {fileID: 1228694481}
  puppetMaster: {fileID: 172621471}
--- !u!1 &2132991796
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2132991797}
  m_Layer: 21
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2132991797
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2132991796}
  serializedVersion: 2
  m_LocalRotation: {x: -0.013830256, y: 0.022153804, z: 0.93106663, w: 0.36391324}
  m_LocalPosition: {x: 0.5656742, y: 0.988, z: 0.038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: -2.942, y: -0.552, z: 137.317}
--- !u!1 &2139117038
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2139117039}
  - component: {fileID: 2139117042}
  - component: {fileID: 2139117041}
  - component: {fileID: 2139117040}
  m_Layer: 0
  m_Name: Joystick1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!224 &2139117039
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2139117038}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 503211931}
  m_Father: {fileID: 1233107368}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 1}
  m_AnchorMax: {x: 0, y: 1}
  m_AnchoredPosition: {x: 278, y: -1306}
  m_SizeDelta: {x: 512, y: 512}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2139117040
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2139117038}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300010, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &2139117041
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2139117038}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &2139117042
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2139117038}
  m_CullTransparentMesh: 0
--- !u!1 &2140445769
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2140445770}
  - component: {fileID: 2140445772}
  - component: {fileID: 2140445771}
  m_Layer: 0
  m_Name: Directional Light
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2140445770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140445769}
  serializedVersion: 2
  m_LocalRotation: {x: 0.40821788, y: -0.23456968, z: 0.10938163, w: 0.8754261}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1706917448}
  m_LocalEulerAnglesHint: {x: 50, y: -30, z: 0}
--- !u!114 &2140445771
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140445769}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 474bcb49853aa07438625e644c072ee6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Version: 3
  m_UsePipelineSettings: 1
  m_AdditionalLightsShadowResolutionTier: 2
  m_LightLayerMask: 1
  m_RenderingLayers: 1
  m_CustomShadowLayers: 0
  m_ShadowLayerMask: 1
  m_ShadowRenderingLayers: 1
  m_LightCookieSize: {x: 1, y: 1}
  m_LightCookieOffset: {x: 0, y: 0}
  m_SoftShadowQuality: 0
--- !u!108 &2140445772
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2140445769}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 0.6886792, g: 0.6886792, b: 0.6886792, a: 1}
  m_Intensity: 1
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 0
  m_AreaSize: {x: 1, y: 1}
  m_BounceIntensity: 1
  m_ColorTemperature: 6570
  m_UseColorTemperature: 0
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 0
  m_LightUnit: 1
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 1
--- !u!1 &2147199035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2147199036}
  - component: {fileID: 2147199040}
  - component: {fileID: 2147199039}
  - component: {fileID: 2147199038}
  - component: {fileID: 2147199037}
  m_Layer: 0
  m_Name: ArrowUp
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2147199036
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199035}
  m_LocalRotation: {x: 0, y: 0, z: 1, w: -0.00000016292068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.25, y: 0.25, z: 0.25}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1914733753}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 1, y: 0}
  m_AnchorMax: {x: 1, y: 0}
  m_AnchoredPosition: {x: -108.5, y: 190.4}
  m_SizeDelta: {x: 256, y: 256}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!114 &2147199037
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199035}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fc509a62d8e6cfc45a8431d8d586b9f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AxisPressedFirstTime:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: UpPressedFirstTime
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisReleased:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: UpReleased
        m_Mode: 1
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 0
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  AxisPressed:
    m_PersistentCalls:
      m_Calls:
      - m_Target: {fileID: 0}
        m_TargetAssemblyTypeName: 
        m_MethodName: VerticalAxisPressed
        m_Mode: 0
        m_Arguments:
          m_ObjectArgument: {fileID: 0}
          m_ObjectArgumentAssemblyTypeName: UnityEngine.Object, UnityEngine
          m_IntArgument: 0
          m_FloatArgument: 1
          m_StringArgument: 
          m_BoolArgument: 0
        m_CallState: 2
  PressedOpacity: 0.5
  AxisValue: 1
  MouseMode: 0
--- !u!114 &2147199038
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199035}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fe87c0e1cc204ed48ad3b37840f39efc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_Sprite: {fileID: 21300014, guid: ********************************, type: 3}
  m_Type: 0
  m_PreserveAspect: 0
  m_FillCenter: 1
  m_FillMethod: 4
  m_FillAmount: 1
  m_FillClockwise: 1
  m_FillOrigin: 0
  m_UseSpriteMesh: 0
  m_PixelsPerUnitMultiplier: 1
--- !u!225 &2147199039
CanvasGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199035}
  m_Enabled: 1
  m_Alpha: 1
  m_Interactable: 1
  m_BlocksRaycasts: 1
  m_IgnoreParentGroups: 0
--- !u!222 &2147199040
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2147199035}
  m_CullTransparentMesh: 0
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 410087041}
  - {fileID: 2132991797}
  - {fileID: 832575519}
  - {fileID: 1494654533}
  - {fileID: 832293010}
  - {fileID: 1207134988}
  - {fileID: 1086374883}
  - {fileID: 1958131080}
  - {fileID: 1706917448}
  - {fileID: 614247166}
  - {fileID: 205932704}
  - {fileID: 972651478}
  - {fileID: 484082368}
  - {fileID: 980428982}
  - {fileID: 1989697419}
  - {fileID: 1582356331}
  - {fileID: 855751751}
  - {fileID: 1773526669}
  - {fileID: 1228694482}
