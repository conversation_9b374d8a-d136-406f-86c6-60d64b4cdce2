﻿#region copyright
// -------------------------------------------------------
// Copyright (C) <PERSON><PERSON><PERSON><PERSON> [https://codestage.net]
// -------------------------------------------------------
#endregion

namespace CodeStage.Maintainer.Issues.Detectors
{
	using System;
	using Core.Scan;

	internal interface IComponentBeginIssueDetector : IComponentBeginScanListener<DetectorResults>
	{
		// return null to check all types
		// checked using Type.IsAssignableFrom() API
		Type[] ComponentTypes { get; }
	}
}