﻿#region copyright
// -------------------------------------------------------
// Copyright (C) <PERSON><PERSON><PERSON><PERSON> [https://codestage.net]
// -------------------------------------------------------
#endregion

namespace CodeStage.Maintainer.Core.Scan
{
	internal interface ISceneBeginScanListener<T> where T : IScanListenerResults
	{
		void SceneBegin(T results, AssetLocation location);
	}
}