using System.Collections.Generic;
using UnityEngine;
using Unity.Mathematics;
using Sirenix.OdinInspector;
using Events;
using DG.Tweening;
using Module.Mono.Animancer.RealsticFemale;
#if UNITY_EDITOR
using UnityEditor;
#endif

namespace Controllers
{
    /// <summary>
    /// Centralized rotation manager to prevent conflicts between multiple rotation systems
    /// Coordinates MovementModule, AdvancedUpperBodyRotationController, RagdollAimingController, etc.
    /// </summary>
    public class CentralizedRotationManager : MonoBehaviour
    {
        [Header("Rotation Priority System")]
        [SerializeField] private bool enableCentralizedRotation = true;
        [SerializeField] private RotationPriority currentPriority = RotationPriority.Movement;
        
        [Header("Rotation Settings")]
        [SerializeField] private float maxRotationSpeed = 180f; // Degrees per second
        [SerializeField] private float rotationSmoothTime = 0.2f;
        [SerializeField] private float targetSwitchCooldown = 0.3f;
        [SerializeField] private float proximityThreshold = 2f; // Distance to target for proximity checks
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool showDebugGizmos = true;
        [SerializeField] private bool showDebugUI = true;
        [SerializeField] private bool logRotationRequests = true;
        [SerializeField] private bool logRotationConflicts = true;
        
        // Rotation state
        private float currentRotationAngle;
        private float targetRotationAngle;
        private float rotationVelocity;
        private float lastTargetSwitchTime;
        private Vector3 lastTargetPosition;
        private bool isRotating;
        
        // System references
        [SerializeField] private Transform playerTransform;
        [SerializeField] private MovementModule movementModule;
        [SerializeField] private AdvancedUpperBodyRotationController upperBodyController;
        [SerializeField] private RootMotion.Demos.RagdollAimingController ragdollAiming;
        
        // Rotation requests
        private RotationRequest currentRequest;
        private bool hasActiveRequest;

        // Debug tracking
        private int totalRotationRequests = 0;
        private int acceptedRequests = 0;
        private int rejectedRequests = 0;
        private float lastRotationRequestTime = 0f;
        private string lastRotationSource = "";
        private List<string> recentRotationLog = new List<string>();
        
        public enum RotationPriority
        {
            Movement = 0,           // Normal movement rotation
            Aiming = 1,            // Aiming at targets
            RagdollAiming = 2,     // RagdollAimingController
            FastRotation = 3,      // Emergency fast rotation
            Override = 4           // Manual override
        }
        
        [System.Serializable]
        public struct RotationRequest
        {
            public RotationPriority priority;
            public Vector3 targetPosition;
            public float rotationSpeed;
            public bool useSmoothing;
            public string requestSource;
            public float requestTime;
            
            public bool IsValid => !targetPosition.Equals(Vector3.zero) && requestTime > 0;
        }
        
        #region Unity Lifecycle
        
        private void Start()
        {
            SetupSystemReferences();
            SubscribeToEvents();
        }
        
        private void Update()
        {
            if (enableCentralizedRotation)
            {
                UpdateRotationSystem();
            }
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        #endregion
        
        #region Initialization
        
        private void SetupSystemReferences()
        {
            movementModule = GetComponent<MovementModule>();
            upperBodyController = GetComponent<AdvancedUpperBodyRotationController>();
            ragdollAiming = GetComponentInChildren<RootMotion.Demos.RagdollAimingController>();
            
            LogDebug($"System references: Movement={movementModule != null}, UpperBody={upperBodyController != null}, Ragdoll={ragdollAiming != null}");
        }
        
        #endregion
        
        #region Event Handling
        
        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnRequireFastRotationEvent>(OnFastRotationRequired);
        }
        
        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnFastRotationRequired);
        }
        
        private void OnAimingStarted(OnAimingOnTargetEvent eventData)
        {
            RequestRotation(RotationPriority.Aiming, eventData.TargetPosition, maxRotationSpeed, true, "AimingModule");
        }
        
        private void OnAimingStopped(OnUnAimingTargetEvent eventData)
        {
            ClearRotationRequest(RotationPriority.Aiming);
        }
        
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            // Only update if we're already aiming or if this is a higher priority
            if (currentPriority <= RotationPriority.Aiming)
            {
                RequestRotation(RotationPriority.Aiming, eventData.TargetPosition, maxRotationSpeed, true, "TargetDetection");
            }
        }
        
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            ClearRotationRequest(RotationPriority.Aiming);
        }
        
        private void OnFastRotationRequired(OnRequireFastRotationEvent eventData)
        {
            RequestRotation(RotationPriority.FastRotation, eventData.TargetPosition, maxRotationSpeed * 2f, false, "FastRotation");
        }
        
        #endregion
        
        #region Rotation Management
        
        /// <summary>
        /// Request rotation with priority system
        /// </summary>
        public bool RequestRotation(RotationPriority priority, Vector3 targetPosition, float speed, bool useSmoothing, string source)
        {
            // Track debug statistics
            totalRotationRequests++;
            lastRotationRequestTime = Time.time;
            lastRotationSource = source;

            // Check if we should accept this request
            if (!ShouldAcceptRotationRequest(priority, targetPosition))
            {
                rejectedRequests++;
                string rejectReason = $"REJECTED: {source} -> Priority={priority}, Target={targetPosition:F1}";
                AddToRotationLog(rejectReason);

                if (logRotationRequests)
                {
                    LogDebug($"Rotation request rejected: Priority={priority}, Source={source}");
                }
                return false;
            }

            // Create new rotation request
            currentRequest = new RotationRequest
            {
                priority = priority,
                targetPosition = targetPosition,
                rotationSpeed = speed,
                useSmoothing = useSmoothing,
                requestSource = source,
                requestTime = Time.time
            };

            hasActiveRequest = true;
            currentPriority = priority;
            lastTargetPosition = targetPosition;
            lastTargetSwitchTime = Time.time;
            acceptedRequests++;

            string acceptReason = $"ACCEPTED: {source} -> Priority={priority}, Target={targetPosition:F1}";
            AddToRotationLog(acceptReason);

            if (logRotationRequests)
            {
                LogDebug($"Rotation request accepted: Priority={priority}, Target={targetPosition}, Source={source}");
            }
            return true;
        }
        
        /// <summary>
        /// Clear rotation request for specific priority
        /// </summary>
        public void ClearRotationRequest(RotationPriority priority)
        {
            if (hasActiveRequest && currentRequest.priority == priority)
            {
                hasActiveRequest = false;
                currentPriority = RotationPriority.Movement;
                LogDebug($"Rotation request cleared: Priority={priority}");
            }
        }
        
        /// <summary>
        /// Check if we should accept a rotation request
        /// </summary>
        private bool ShouldAcceptRotationRequest(RotationPriority priority, Vector3 targetPosition)
        {
            // Validate target position
            if (targetPosition == Vector3.zero || float.IsNaN(targetPosition.x) || float.IsNaN(targetPosition.y) || float.IsNaN(targetPosition.z))
            {
                LogDebug($"Invalid target position: {targetPosition}, rejecting rotation request");
                return false;
            }

            // Always accept higher priority requests
            if (priority > currentPriority)
            {
                LogDebug($"Accepting higher priority request: {priority} > {currentPriority}");
                return true;
            }

            // Check cooldown for same priority
            if (priority == currentPriority && Time.time - lastTargetSwitchTime < targetSwitchCooldown)
            {
                // Allow small target updates without cooldown
                if (Vector3.Distance(lastTargetPosition, targetPosition) < 0.5f)
                {
                    LogDebug($"Accepting small target update: distance = {Vector3.Distance(lastTargetPosition, targetPosition):F2}m");
                    return true;
                }

                LogDebug($"Rejecting request due to cooldown: {Time.time - lastTargetSwitchTime:F2}s < {targetSwitchCooldown:F2}s");
                return false;
            }

            // Check if RagdollAiming is active and should take priority
            if (IsRagdollAimingActive() && priority < RotationPriority.RagdollAiming)
            {
                LogDebug($"Rejecting request: RagdollAiming active with higher priority");
                return false;
            }

            // Check proximity to prevent spinning around close targets
            float distanceToTarget = Vector3.Distance(playerTransform.position, targetPosition);
            if (distanceToTarget < proximityThreshold && priority == RotationPriority.Aiming)
            {
                LogDebug($"Target too close ({distanceToTarget:F2}m), rejecting rotation request");
                return false;
            }

            LogDebug($"Accepting rotation request: Priority={priority}, Distance={distanceToTarget:F2}m");
            return true;
        }
        
        /// <summary>
        /// Update the centralized rotation system
        /// </summary>
        private void UpdateRotationSystem()
        {
            // Check for rotation request timeouts
            CheckRotationTimeout();

            // Validate rotation state and resolve conflicts if needed
            if (enableDebugLogging)
            {
                ResolveRotationConflicts();
            }

            // Check if RagdollAiming should take control
            if (IsRagdollAimingActive())
            {
                DisableOtherRotationSystems();
                return;
            }

            // Process current rotation request
            if (hasActiveRequest && currentRequest.IsValid)
            {
                ProcessRotationRequest();
            }
            else
            {
                // No active request, allow normal movement rotation
                EnableMovementRotation();
            }
        }
        
        /// <summary>
        /// Process the current rotation request
        /// </summary>
        private void ProcessRotationRequest()
        {
            Vector3 directionToTarget = currentRequest.targetPosition - playerTransform.position;
            directionToTarget.y = 0; // Keep horizontal
            
            if (directionToTarget.magnitude < 0.1f)
                return;
            
            // Calculate target angle
            float targetAngle = Mathf.Atan2(directionToTarget.x, directionToTarget.z) * Mathf.Rad2Deg;
            
            // Apply rotation speed limiting
            float maxAngleChange = currentRequest.rotationSpeed * Time.deltaTime;
            float angleDifference = Mathf.DeltaAngle(currentRotationAngle, targetAngle);
            
            if (Mathf.Abs(angleDifference) > maxAngleChange)
            {
                targetAngle = currentRotationAngle + Mathf.Sign(angleDifference) * maxAngleChange;
            }
            
            // Apply rotation
            if (currentRequest.useSmoothing)
            {
                currentRotationAngle = Mathf.SmoothDampAngle(currentRotationAngle, targetAngle, ref rotationVelocity, rotationSmoothTime);
            }
            else
            {
                currentRotationAngle = targetAngle;
            }
            
            // Apply to transform
            playerTransform.rotation = Quaternion.Euler(0, currentRotationAngle, 0);
            isRotating = true;
            
            // Disable other rotation systems
            DisableOtherRotationSystems();
            
            LogDebug($"Centralized rotation applied: {currentRotationAngle:F1}° (Target: {targetAngle:F1}°)");
        }
        
        #endregion
        
        #region System Coordination
        
        /// <summary>
        /// Check if RagdollAiming is active
        /// </summary>
        private bool IsRagdollAimingActive()
        {
            return ragdollAiming != null && ragdollAiming.enabled && ragdollAiming.weight > 0.1f;
        }
        
        /// <summary>
        /// Disable other rotation systems when centralized rotation is active
        /// </summary>
        private void DisableOtherRotationSystems()
        {
            // MovementModule now checks IsCentralizedRotationActive() before applying rotation
            // No direct disabling needed as MovementModule handles this internally

            // Disable AdvancedUpperBodyRotationController player rotation
            if (upperBodyController != null)
            {
                upperBodyController.SetRotateEntirePlayer(false);
            }

            LogDebug($"Other rotation systems disabled for priority: {currentPriority}");
        }
        
        /// <summary>
        /// Enable normal movement rotation
        /// </summary>
        private void EnableMovementRotation()
        {
            if (upperBodyController != null)
            {
                upperBodyController.SetRotateEntirePlayer(true);
            }
            
            isRotating = false;
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Check if centralized rotation is currently active
        /// </summary>
        public bool IsCentralizedRotationActive()
        {
            return enableCentralizedRotation && (hasActiveRequest || IsRagdollAimingActive());
        }
        
        /// <summary>
        /// Get current rotation status
        /// </summary>
        public string GetRotationStatus()
        {
            return $"Priority: {currentPriority}, Active: {hasActiveRequest}, Rotating: {isRotating}, " +
                   $"Current: {currentRotationAngle:F1}°, Target: {targetRotationAngle:F1}°";
        }
        
        /// <summary>
        /// Force stop all rotation
        /// </summary>
        public void StopAllRotation()
        {
            hasActiveRequest = false;
            currentPriority = RotationPriority.Movement;
            isRotating = false;
            EnableMovementRotation();
            LogDebug("All rotation stopped");
        }

        /// <summary>
        /// Public method to validate rotation state (for testing)
        /// </summary>
        public bool ValidateAndResolveRotationState()
        {
            bool isValid = ValidateRotationState();
            if (!isValid)
            {
                ResolveRotationConflicts();
                // Re-validate after resolution
                isValid = ValidateRotationState();
            }
            return isValid;
        }

        /// <summary>
        /// Check if a rotation request has timed out and should be cleared
        /// </summary>
        private void CheckRotationTimeout()
        {
            if (hasActiveRequest && currentRequest.IsValid)
            {
                // Clear requests that are older than 5 seconds (except Override priority)
                float requestAge = Time.time - currentRequest.requestTime;
                if (requestAge > 5f && currentRequest.priority != RotationPriority.Override)
                {
                    LogDebug($"Rotation request timed out after {requestAge:F1}s, clearing request");
                    ClearRotationRequest(currentRequest.priority);
                }
            }
        }

        /// <summary>
        /// Get the current active rotation priority
        /// </summary>
        public RotationPriority GetCurrentPriority()
        {
            return currentPriority;
        }

        /// <summary>
        /// Check if a specific priority is currently active
        /// </summary>
        public bool IsPriorityActive(RotationPriority priority)
        {
            return hasActiveRequest && currentRequest.priority == priority;
        }

        /// <summary>
        /// Validate that only one rotation system is active at a time
        /// </summary>
        public bool ValidateRotationState()
        {
            bool isValid = true;
            List<string> activeRotationSystems = new List<string>();

            // Check CentralizedRotationManager
            if (hasActiveRequest)
            {
                activeRotationSystems.Add($"CentralizedRotationManager({currentRequest.requestSource})");
            }

            // Check RagdollAiming
            if (IsRagdollAimingActive())
            {
                activeRotationSystems.Add("RagdollAimingController");
            }

            // Check MovementModule DOTween rotation
            if (movementModule != null)
            {
                // MovementModule should not be rotating if centralized rotation is active
                // This is handled by MovementModule's internal checks
            }

            // Log validation results
            if (activeRotationSystems.Count > 1)
            {
                isValid = false;
                LogDebug($"VALIDATION FAILED: Multiple rotation systems active: {string.Join(',', activeRotationSystems)}");
            }
            else if (activeRotationSystems.Count == 1)
            {
                LogDebug($"Validation passed: Single rotation system active: {activeRotationSystems[0]}");
            }
            else
            {
                LogDebug("Validation passed: No rotation systems active");
            }

            return isValid;
        }

        /// <summary>
        /// Force resolve rotation conflicts by stopping all lower priority systems
        /// </summary>
        public void ResolveRotationConflicts()
        {
            if (!ValidateRotationState())
            {
                LogDebug("Resolving rotation conflicts...");

                // If RagdollAiming is active, it takes highest priority
                if (IsRagdollAimingActive())
                {
                    // Clear any centralized rotation requests that are lower priority
                    if (hasActiveRequest && currentRequest.priority < RotationPriority.RagdollAiming)
                    {
                        LogDebug("Clearing lower priority rotation request for RagdollAiming");
                        hasActiveRequest = false;
                        currentPriority = RotationPriority.RagdollAiming;
                    }
                }

                // Ensure other systems are properly disabled
                DisableOtherRotationSystems();

                LogDebug("Rotation conflicts resolved");
            }
        }
        
        #endregion
        
        #region Debug
        
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[CentralizedRotationManager] {message}");
            }
        }

        /// <summary>
        /// Add entry to rotation log for debugging
        /// </summary>
        private void AddToRotationLog(string entry)
        {
            string timestampedEntry = $"[{Time.time:F2}s] {entry}";
            recentRotationLog.Add(timestampedEntry);

            // Keep only last 20 entries
            if (recentRotationLog.Count > 20)
            {
                recentRotationLog.RemoveAt(0);
            }
        }

        /// <summary>
        /// Get debug statistics for UI display
        /// </summary>
        public string GetDebugStats()
        {
            float acceptanceRate = totalRotationRequests > 0 ? (acceptedRequests / (float)totalRotationRequests) * 100f : 0f;

            return $"Rotation Debug Stats:\n" +
                   $"Total Requests: {totalRotationRequests}\n" +
                   $"Accepted: {acceptedRequests}\n" +
                   $"Rejected: {rejectedRequests}\n" +
                   $"Acceptance Rate: {acceptanceRate:F1}%\n" +
                   $"Current Priority: {currentPriority}\n" +
                   $"Active Request: {hasActiveRequest}\n" +
                   $"Last Source: {lastRotationSource}\n" +
                   $"Last Request: {Time.time - lastRotationRequestTime:F1}s ago";
        }

        /// <summary>
        /// Get recent rotation log for debugging
        /// </summary>
        public List<string> GetRotationLog()
        {
            return new List<string>(recentRotationLog);
        }

        /// <summary>
        /// Clear debug statistics
        /// </summary>
        [Button("Clear Debug Stats")]
        public void ClearDebugStats()
        {
            totalRotationRequests = 0;
            acceptedRequests = 0;
            rejectedRequests = 0;
            recentRotationLog.Clear();
            LogDebug("Debug statistics cleared");
        }
        
        private void OnGUI()
        {
            if (!showDebugUI || !Application.isPlaying) return;

            // Create debug UI window
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.BeginVertical("box");

            // Create bold style for headers
            GUIStyle boldStyle = new GUIStyle(GUI.skin.label) { fontStyle = FontStyle.Bold };

            GUILayout.Label("Centralized Rotation Manager Debug", boldStyle);
            GUILayout.Space(5);

            // Current status
            GUILayout.Label($"Status: {(enableCentralizedRotation ? "ENABLED" : "DISABLED")}");
            GUILayout.Label($"Priority: {currentPriority}");
            GUILayout.Label($"Active Request: {hasActiveRequest}");
            GUILayout.Label($"Is Rotating: {isRotating}");

            if (hasActiveRequest)
            {
                GUILayout.Space(5);
                GUILayout.Label("Current Request:", boldStyle);
                GUILayout.Label($"Source: {currentRequest.requestSource}");
                GUILayout.Label($"Target: {currentRequest.targetPosition:F1}");
                GUILayout.Label($"Speed: {currentRequest.rotationSpeed:F1}°/s");
                GUILayout.Label($"Age: {Time.time - currentRequest.requestTime:F1}s");
            }

            GUILayout.Space(5);
            GUILayout.Label("Statistics:", boldStyle);
            GUILayout.Label($"Total Requests: {totalRotationRequests}");
            GUILayout.Label($"Accepted: {acceptedRequests}");
            GUILayout.Label($"Rejected: {rejectedRequests}");

            if (GUILayout.Button("Clear Stats"))
            {
                ClearDebugStats();
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }

        private void OnDrawGizmosSelected()
        {
            if (!showDebugGizmos || !Application.isPlaying) return;

            Vector3 center = transform.position + Vector3.up * 2f;

            // Draw current rotation
            Gizmos.color = Color.green;
            Vector3 currentDir = Quaternion.Euler(0, currentRotationAngle, 0) * Vector3.forward;
            Gizmos.DrawLine(center, center + currentDir * 2f);

            // Draw target position
            if (hasActiveRequest)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(currentRequest.targetPosition, 0.3f);
                Gizmos.DrawLine(center, currentRequest.targetPosition);

                // Draw proximity threshold
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position, proximityThreshold);
            }
        }
        
        #endregion
    }
}
