using UnityEngine;
using Controllers;
using Module;
using Module.Mono.Animancer.RealsticFemale;
using Sirenix.OdinInspector;

namespace DebugRotation
{
    /// <summary>
    /// Test script to verify the rotation system integration works correctly
    /// </summary>
    public class RotationSystemTester : MonoBehaviour
    {
        [Header("Test Components")]
        [SerializeField] private CentralizedRotationManager rotationManager;
        [SerializeField] private MovementModule movementModule;
        [SerializeField] private Transform testTarget;
        
        [Header("Test Settings")]
        [SerializeField] private bool enableDebugLogging = true;
        [SerializeField] private float testTargetDistance = 5f;
        
        [Header("Test Results")]
        [ReadOnly] [SerializeField] private bool rotationSystemActive;
        [ReadOnly] [SerializeField] private string currentRotationPriority;
        [ReadOnly] [SerializeField] private bool lowerBodySyncActive;
        
        private void Start()
        {
            // Auto-find components if not assigned
            if (rotationManager == null)
                rotationManager = FindObjectOfType<CentralizedRotationManager>();
                
            if (movementModule == null)
                movementModule = FindObjectOfType<MovementModule>();
                
            // Create test target if not assigned
            if (testTarget == null)
            {
                GameObject targetObj = new GameObject("TestTarget");
                testTarget = targetObj.transform;
                testTarget.position = transform.position + Vector3.forward * testTargetDistance;
            }
        }
        
        private void Update()
        {
            UpdateTestResults();
        }
        
        private void UpdateTestResults()
        {
            if (rotationManager != null)
            {
                rotationSystemActive = rotationManager.IsCentralizedRotationActive();
                currentRotationPriority = rotationManager.GetCurrentPriority().ToString();
            }
            
            // Check if lower body sync would be active
            lowerBodySyncActive = movementModule != null && 
                                 movementModule.characterParameters != null && 
                                 movementModule.characterParameters.IsAiming;
        }
        
        [Button("Test Fast Rotation")]
        public void TestFastRotation()
        {
            if (rotationManager != null && testTarget != null)
            {
                bool success = rotationManager.RequestRotation(
                    CentralizedRotationManager.RotationPriority.FastRotation,
                    testTarget.position,
                    180f, // 180 degrees per second
                    false, // No smoothing
                    "RotationSystemTester"
                );
                
                LogTest($"Fast rotation test: {(success ? "SUCCESS" : "FAILED")}");
            }
        }
        
        [Button("Test Aiming Rotation")]
        public void TestAimingRotation()
        {
            if (rotationManager != null && testTarget != null)
            {
                bool success = rotationManager.RequestRotation(
                    CentralizedRotationManager.RotationPriority.Aiming,
                    testTarget.position,
                    90f, // 90 degrees per second
                    true, // Use smoothing
                    "RotationSystemTester"
                );
                
                LogTest($"Aiming rotation test: {(success ? "SUCCESS" : "FAILED")}");
            }
        }
        
        [Button("Test Movement Rotation")]
        public void TestMovementRotation()
        {
            if (rotationManager != null && testTarget != null)
            {
                bool success = rotationManager.RequestRotation(
                    CentralizedRotationManager.RotationPriority.Movement,
                    testTarget.position,
                    45f, // 45 degrees per second
                    true, // Use smoothing
                    "RotationSystemTester"
                );
                
                LogTest($"Movement rotation test: {(success ? "SUCCESS" : "FAILED")}");
            }
        }
        
        [Button("Stop All Rotation")]
        public void StopAllRotation()
        {
            if (rotationManager != null)
            {
                rotationManager.StopAllRotation();
                LogTest("All rotation stopped");
            }
        }
        
        [Button("Validate Rotation State")]
        public void ValidateRotationState()
        {
            if (rotationManager != null)
            {
                bool isValid = rotationManager.ValidateAndResolveRotationState();
                LogTest($"Rotation state validation: {(isValid ? "VALID" : "INVALID - RESOLVED")}");
            }
        }
        
        [Button("Move Test Target")]
        public void MoveTestTarget()
        {
            if (testTarget != null)
            {
                // Move target to a random position around the player
                float angle = Random.Range(0f, 360f) * Mathf.Deg2Rad;
                Vector3 newPosition = transform.position + new Vector3(
                    Mathf.Sin(angle) * testTargetDistance,
                    0f,
                    Mathf.Cos(angle) * testTargetDistance
                );
                
                testTarget.position = newPosition;
                LogTest($"Test target moved to: {newPosition}");
            }
        }
        
        private void LogTest(string message)
        {
            if (enableDebugLogging)
            {
                UnityEngine.Debug.Log($"[RotationSystemTester] {message}");
            }
        }
        
        private void OnDrawGizmos()
        {
            // Draw test target
            if (testTarget != null)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(testTarget.position, 0.5f);
                Gizmos.DrawLine(transform.position, testTarget.position);
            }
            
            // Draw rotation state
            if (rotationSystemActive)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireCube(transform.position + Vector3.up * 3f, Vector3.one * 0.5f);
            }
        }
    }
}
